# SourceFlex Information

## Summary
SourceFlex is a modern, multi-tenant recruitment and bench sales platform built with Next.js 14. It features AI-powered semantic search, desk-based role separation, and comprehensive fraud prevention. The platform leverages Hasura with nHost for its backend API, providing GraphQL capabilities with PostgreSQL database integration.

## Structure
- **src/**: Main application code
  - **actions/**: Server actions for mutations
  - **app/**: Next.js App Router pages
  - **components/**: Reusable UI components
  - **modules/**: Feature-specific modules (auth, candidates, jobs, etc.)
  - **providers/**: Context providers
  - **types/**: TypeScript definitions including GraphQL generated types
- **database/**: Database schema and migrations for PostgreSQL
- **docs/**: Development standards and documentation

## Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5+
**Framework**: Next.js 15.3.4
**Build System**: Next.js build system
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- **Next.js**: 15.3.4 with App Router
- **React**: v19.0.0
- **nHost**: Backend services integration (@nhost/nextjs, @nhost/react, @nhost/react-apollo)
- **Apollo Client**: GraphQL client (@apollo/client)
- **GraphQL**: API query language
- **Stripe**: Payment processing (@stripe/react-stripe-js, @stripe/stripe-js)
- **ShadCN UI**: Component library with Radix UI primitives
- **Tailwind CSS**: Utility-first styling

**Development Dependencies**:
- **GraphQL Codegen**: Type generation from GraphQL schema (@graphql-codegen/cli)
- **ESLint**: Code linting
- **TypeScript**: v5 for type checking

## Build & Installation
```bash
# Install dependencies
npm install

# Generate GraphQL types from Hasura schema
npm run codegen

# Development server
npm run dev

# Production build
npm run build
```

## API & Database
**API Layer**: Hasura GraphQL via nHost
**Database**: PostgreSQL with pgvector extension
**GraphQL Generation**: Automatic type generation via GraphQL Codegen
**Key Features**:
- Auto-generated GraphQL API from PostgreSQL schema
- Real-time subscriptions via Hasura
- Row Level Security for multi-tenant data isolation
- Vector embeddings for semantic search with pgvector

## Hasura & nHost Integration
**Configuration**: Defined in codegen.ts for GraphQL schema generation
**Authentication**: nHost Auth with JWT integration to Hasura
**GraphQL Endpoint**: Configured via environment variables
```
NEXT_PUBLIC_NHOST_SUBDOMAIN
NEXT_PUBLIC_NHOST_REGION
NEXT_PUBLIC_GRAPHQL_ENDPOINT
```
**Type Generation**: Automatic GraphQL type generation for type-safe queries
**Data Access**: Apollo Client integration with nHost for GraphQL operations

## Architecture
**Multi-Tenancy**: All data scoped to company_id with desk separation
**Authentication Flow**: nHost Auth with role-based permissions
**Data Flow**: Client → Apollo Client → Hasura GraphQL → PostgreSQL
**Storage**: nHost Storage for document management
**Security**: Row Level Security policies at database level

## Key Features
- **Bench ID System**: Unique candidate identification
- **Resume Fraud Prevention**: Version tracking and duplicate detection
- **Semantic Search**: AI-powered candidate and job matching with pgvector
- **Multi-Tenant Architecture**: Company-level isolation with subscription-based feature gating
- **Real-time Data**: Hasura subscriptions for live updates

## Deployment
**Recommended Platform**: Vercel
**Database & API**: nHost (PostgreSQL + Hasura)
**Environment Setup**:
1. Deploy schema to nHost PostgreSQL instance
2. Configure Hasura permissions
3. Enable pgvector extension
4. Set up environment variables for nHost connection
# SourceFlex

A modern, multi-tenant recruitment and bench sales platform built with Next.js 14, featuring AI-powered semantic search, desk-based role separation, and comprehensive fraud prevention.

## 🌟 Key Features

### Multi-Tenant Architecture
- **Company-level isolation** with subscription-based feature gating
- **Desk separation** between Recruitment and Bench Sales teams
- **Role-based permissions** with granular access control

### Unique Recruiting Features
- **Bench ID System**: Unique FFF+LLL+MMDD+SSSS format for candidate identification
- **Resume Fraud Prevention**: Version tracking and duplicate detection
- **Semantic Search**: AI-powered candidate and job matching with pgvector
- **Real-time Notifications**: Activity tracking and automated alerts

### Technology Stack
- **Frontend**: Next.js 14 + TypeScript + App Router
- **UI**: ShadCN UI (New York style) + Tailwind CSS
- **Backend**: nHost (PostgreSQL + Auth + Storage) + Hasura GraphQL
- **Database**: PostgreSQL with pgvector for semantic search
- **Authentication**: nHost Auth with multi-tenant support

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm/yarn/pnpm
- nHost account and project

### Installation

1. **Clone and install dependencies**
   ```bash
   git clone <repository-url>
   cd sourceflex
   npm install
   ```

2. **Environment setup**
   ```bash
   cp .env.example .env.local
   # Configure your nHost credentials
   ```

3. **Database setup**
   - Deploy the schema from `database/schema.sql` to your nHost PostgreSQL instance
   - Configure Hasura permissions
   - Enable pgvector extension

4. **Generate GraphQL types**
   ```bash
   npm run codegen
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── actions/           # Server actions for mutations
├── app/              # Next.js App Router pages
├── components/       # Reusable UI components
│   ├── ui/          # ShadCN base components
│   ├── forms/       # Form components
│   ├── layout/      # Layout components
│   └── dialogs/     # Dialog components
├── config/          # Configuration files
├── hooks/           # Custom React hooks
├── lib/             # Utility libraries
├── modules/         # Feature modules
│   ├── auth/        # Authentication & authorization
│   ├── candidates/  # Candidate management
│   ├── jobs/        # Job posting & management
│   ├── clients/     # Client relationship management
│   ├── bench/       # Bench sales operations
│   ├── submissions/ # Candidate submissions
│   ├── interviews/  # Interview scheduling
│   ├── placements/  # Placement tracking
│   ├── documents/   # Document management
│   ├── communication/ # Communication tracking
│   ├── search/      # Advanced search features
│   └── ai/          # AI-powered features
├── providers/       # Context providers
└── types/          # TypeScript definitions
    └── generated/  # GraphQL generated types
```

## 🎯 Core Modules

### 1. Authentication (`/modules/auth`)
- Multi-tenant authentication with nHost
- Role-based access control
- Desk-based permissions

### 2. Candidates (`/modules/candidates`)
- Unique Bench ID generation (FFF+LLL+MMDD+SSSS)
- Skills tracking and categorization
- Availability management

### 3. Jobs (`/modules/jobs`)
- Job posting and requirement management
- Client assignment and tracking
- Salary and fee management

### 4. Documents (`/modules/documents`)
- Resume upload and management
- Fraud prevention with versioning
- OCR text extraction for search

### 5. Search (`/modules/search`)
- Traditional full-text search
- AI-powered semantic search with embeddings
- Saved searches and alerts

## 🔧 Development

### Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run codegen      # Generate GraphQL types
npm run codegen:watch # Watch mode for GraphQL codegen
```

### Code Standards
- **File Size Limit**: Maximum 300 lines per file
- **Multi-tenant**: Always scope queries by company_id and desk_id
- **TypeScript**: Strict type safety with no `any` types
- **Components**: Follow ShadCN UI patterns
- **Forms**: React Hook Form + Zod validation

See [Development Standards](./docs/development-standards.md) for detailed guidelines.

### Database Schema
Complete PostgreSQL schema with:
- Multi-tenant isolation with RLS
- Semantic search with pgvector
- Audit logging and activity tracking
- Subscription-based feature gating

See [Database Documentation](./database/README.md) for schema details.

## 🏗️ Architecture Decisions

### Multi-Tenancy
- **Company-level isolation**: All data scoped to company_id
- **Desk separation**: Recruitment vs Bench Sales workflows
- **Row Level Security**: Database-enforced tenant isolation

### Subscription Model
- **Starter**: Basic features, 5 users, 50 jobs, 500 candidates
- **Professional**: Advanced search, AI features, 15 users
- **Enterprise**: Full features, unlimited usage, API access

### Unique Features
- **Bench ID System**: Prevents candidate duplication across desks
- **Resume Fraud Detection**: File hashing and version control
- **Semantic Search**: Vector embeddings for intelligent matching

## 📊 Key Technologies

### Frontend Stack
- **Next.js 14**: App Router with Server Components
- **TypeScript**: Full type safety throughout
- **ShadCN UI**: Modern, accessible component library
- **Tailwind CSS**: Utility-first styling

### Backend Stack
- **nHost**: Managed backend with PostgreSQL + Auth + Storage
- **Hasura**: Auto-generated GraphQL API
- **PostgreSQL**: Primary database with pgvector extension
- **Apollo Client**: GraphQL client with caching

### Development Tools
- **ESLint**: Strict linting rules for code quality
- **GraphQL Codegen**: Automatic type generation
- **React Hook Form**: Performant form handling
- **Zod**: Runtime type validation

## 🚀 Deployment

### Production Deployment
1. **Build the application**
   ```bash
   npm run build
   ```

2. **Deploy to Vercel** (recommended)
   - Connect your repository to Vercel
   - Configure environment variables
   - Deploy automatically on push

3. **Database Migration**
   - Apply schema changes via nHost console
   - Update Hasura permissions
   - Run data migrations if needed

### Environment Variables
```env
# nHost Configuration
NEXT_PUBLIC_NHOST_SUBDOMAIN=your-subdomain
NEXT_PUBLIC_NHOST_REGION=us-east-1

# GraphQL Endpoint
NEXT_PUBLIC_GRAPHQL_ENDPOINT=https://your-subdomain.hasura.us-east-1.nhost.run/v1/graphql

# Optional: Custom domain
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow development standards** (see docs/development-standards.md)
4. **Write tests** for new functionality
5. **Submit a pull request**

### Development Workflow
1. Check existing code structure before adding new files
2. Keep files under 300 lines (split at 200 lines)
3. Always scope data by company_id and desk_id
4. Use TypeScript strictly (no `any` types)
5. Follow the established module structure

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Open a GitHub issue
- **Email**: [Your support email]

## 🗺️ Roadmap

### Phase 1 (Current)
- [x] Project setup and architecture
- [x] Database schema design
- [x] Development standards
- [ ] Authentication module
- [ ] Basic candidate management
- [ ] Job posting functionality

### Phase 2
- [ ] Advanced search with AI
- [ ] Document management
- [ ] Interview scheduling
- [ ] Communication tracking

### Phase 3
- [ ] Placement tracking
- [ ] Analytics and reporting
- [ ] Mobile responsiveness
- [ ] API for integrations

---

Built with ❤️ for modern recruitment teams

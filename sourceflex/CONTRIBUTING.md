        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </>
  );
}
```

### Form Patterns
```typescript
// Standard form structure
function EntityForm({ entity, onSubmit }: EntityFormProps) {
  const form = useForm<EntityFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(entitySchema),
    defaultValues: entity || defaultValues
  });
  
  const handleSubmit = async (data: EntityFormData) => {
    try {
      await onSubmit(data);
      toast.success('Entity saved successfully');
    } catch (error) {
      toast.error('Failed to save entity');
    }
  };
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        {/* Form fields */}
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
```

## 🗃️ Database Changes

### Schema Modifications
1. **Always create migrations** for schema changes
2. **Update the documentation** in `database/README.md`
3. **Consider multi-tenant implications**
4. **Add appropriate indexes** for performance

### GraphQL Changes
1. **Update GraphQL queries/mutations** in module files
2. **Run codegen** to update TypeScript types
3. **Update component props** if types changed
4. **Test all affected components**

```bash
# After GraphQL schema changes
npm run codegen
```

## 🔒 Security Guidelines

### Authentication & Authorization
- Always check user permissions before rendering UI
- Validate user access to resources
- Use proper error messages (don't leak sensitive info)

### Data Validation
```typescript
// Server-side validation
const candidateSchema = z.object({
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  email: z.string().email(),
  skills: z.array(z.string()).min(1)
});

// Client-side sanitization
import { sanitize } from 'dompurify';

function sanitizeInput(input: string): string {
  return sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}
```

### Multi-Tenant Security
- Never expose data across tenants
- Always include company_id in queries
- Validate user belongs to requested company
- Use RLS policies as backup security layer

## 📚 Documentation Standards

### Component Documentation
```typescript
/**
 * CandidateCard displays candidate information in a card format
 * 
 * @param candidate - The candidate object to display
 * @param onEdit - Callback when edit button is clicked
 * @param showActions - Whether to show action buttons (default: true)
 * 
 * @example
 * ```tsx
 * <CandidateCard
 *   candidate={candidate}
 *   onEdit={(candidate) => setEditingCandidate(candidate)}
 *   showActions={true}
 * />
 * ```
 */
```

### README Updates
- Update feature lists when adding new functionality
- Add new environment variables to documentation
- Update setup instructions if process changes
- Include screenshots for major UI changes

## 🐛 Bug Reports

### Information to Include
- **Environment details** (browser, OS, Node version)
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Screenshots or videos** if applicable
- **Error messages** from console/network tab
- **User role and permissions** context

### Bug Fix Process
1. **Reproduce the issue** locally
2. **Write a failing test** that demonstrates the bug
3. **Fix the issue** with minimal changes
4. **Ensure the test passes**
5. **Verify no regressions** in related functionality

## 🚀 Feature Requests

### Feature Proposal Format
```markdown
## Feature: [Feature Name]

### Problem Statement
What problem does this solve?

### Proposed Solution
How should it work?

### User Stories
- As a [user type], I want [goal] so that [benefit]

### Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2

### Design Considerations
- Multi-tenant implications
- Performance impact
- Security considerations
- Database schema changes

### Implementation Plan
1. Step 1
2. Step 2
3. Step 3
```

## 📝 Pull Request Guidelines

### PR Checklist
- [ ] **Code follows standards** (ESLint passes)
- [ ] **File size limits** respected (under 300 lines)
- [ ] **Multi-tenant scoping** implemented
- [ ] **TypeScript types** properly defined
- [ ] **Error handling** implemented
- [ ] **Loading states** added where needed
- [ ] **Tests written/updated**
- [ ] **Documentation updated**
- [ ] **GraphQL types regenerated** if needed
- [ ] **No console.log statements** in production code

### PR Title Format
```
type(scope): description

Examples:
feat(candidates): add bulk import functionality
fix(auth): resolve session timeout issue
docs(contributing): add testing guidelines
```

### PR Description Template
```markdown
## Description
Brief summary of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots
(If applicable)

## Related Issues
Fixes #[issue number]

## Checklist
- [ ] Code follows project standards
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## 🔄 Review Process

### What We Look For
1. **Code Quality**
   - Follows established patterns
   - Proper error handling
   - Clean, readable code

2. **Architecture Compliance**
   - Multi-tenant design
   - Module structure adherence
   - Performance considerations

3. **Security**
   - Proper data validation
   - Permission checks
   - Input sanitization

4. **Testing**
   - Adequate test coverage
   - Edge cases considered
   - Manual testing completed

### Response to Feedback
- **Address all comments** before requesting re-review
- **Ask questions** if feedback is unclear
- **Explain your reasoning** for implementation choices
- **Update tests** based on feedback

## 🎯 Common Patterns

### Loading States
```typescript
function CandidateList() {
  const { candidates, loading, error } = useCandidates();
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (candidates.length === 0) return <EmptyState />;
  
  return (
    <div>
      {candidates.map(candidate => (
        <CandidateCard key={candidate.id} candidate={candidate} />
      ))}
    </div>
  );
}
```

### Error Handling
```typescript
function useCreateCandidate() {
  const [createCandidate] = useMutation(CREATE_CANDIDATE, {
    onError: (error) => {
      if (error.message.includes('duplicate')) {
        toast.error('Candidate already exists');
      } else {
        toast.error('Failed to create candidate');
      }
    },
    onCompleted: () => {
      toast.success('Candidate created successfully');
    }
  });
  
  return { createCandidate };
}
```

### Permission Checks
```typescript
function usePermissions() {
  const { user } = useAuth();
  
  const hasPermission = (resource: string, action: string, entity?: any) => {
    // Check user role and permissions
    // Consider entity ownership if applicable
    return checkPermission(user, resource, action, entity);
  };
  
  return { hasPermission };
}
```

## 📞 Getting Help

### Where to Ask Questions
- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For general questions
- **Pull Request Comments**: For code-specific questions
- **Email**: [maintainer email] for private concerns

### Before Asking
1. **Search existing issues** and discussions
2. **Check the documentation** thoroughly
3. **Review the code** for similar implementations
4. **Try to reproduce** the issue with minimal code

## 📈 Performance Guidelines

### Query Optimization
- Use pagination for large lists
- Implement proper loading states
- Cache frequently accessed data
- Use GraphQL fragments for reusable fields

### Component Optimization
- Memoize expensive components
- Use proper dependency arrays in hooks
- Debounce user inputs
- Implement virtual scrolling for large lists

### Bundle Size
- Import only what you need
- Use dynamic imports for large components
- Optimize images and assets
- Monitor bundle analyzer reports

## 🎉 Recognition

Contributors who make significant improvements to SourceFlex will be:
- Listed in the project contributors
- Mentioned in release notes
- Given recognition in project documentation

Thank you for contributing to SourceFlex! 🚀
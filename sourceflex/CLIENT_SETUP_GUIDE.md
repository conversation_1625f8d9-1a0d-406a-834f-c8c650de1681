# ✅ SourceFlex nHost Client Setup Guide

## 🎯 **Current Status: Database Schema is Ready!**

Great news! Your SourceFlex database is already set up with a complete schema. Here's what I found:

### **📋 Your Project Details:**
- **Project Name**: SourceFlex
- **Subdomain**: `ovceoopmxkuwppiemjjz`  
- **Region**: ap-south-1 (Asia Pacific - Mumbai)
- **GraphQL Endpoint**: `https://ovceoopmxkuwppiemjjz.graphql.ap-south-1.nhost.run/v1`
- **Admin Secret**: `8Rh10SmjSkaD2$^81^H%jK!Is'UwSOu7`

### **🏗️ Database Tables Already Created:**
✅ **Authentication System** - Complete nHost auth setup
✅ **Companies** - Multi-tenant organization structure  
✅ **Desks** - Desk-based workflow management
✅ **User Profiles** - Extended user management with roles
✅ **Candidates** - Full candidate management with bench IDs
✅ **Jobs** - Job posting and management
✅ **Clients** - Client relationship management
✅ **Submissions** - Candidate job submissions
✅ **Interviews** - Interview scheduling and tracking
✅ **Placements** - Placement tracking and management
✅ **Notifications** - Notification system
✅ **Activity Logs** - Full audit trail
✅ **Metrics** - Analytics and reporting
✅ **Search Analytics** - Search tracking
✅ **Saved Searches** - User search preferences
✅ **Subscription Plans** - Billing and subscription management
✅ **Stripe Integration** - Payment processing (customers, invoices, subscriptions)
✅ **File Storage** - Document management
✅ **Platform Admin** - SourceFlex team administration

## 🔧 **Client Configuration Setup**

### **1. Environment Variables** 
Update your `.env.local` file:

```bash
# nHost Configuration
NEXT_PUBLIC_NHOST_SUBDOMAIN=ovceoopmxkuwppiemjjz
NEXT_PUBLIC_NHOST_REGION=ap-south-1
NHOST_ADMIN_SECRET=8Rh10SmjSkaD2$^81^H%jK!Is'UwSOu7

# GraphQL Endpoint
NEXT_PUBLIC_GRAPHQL_ENDPOINT=https://ovceoopmxkuwppiemjjz.graphql.ap-south-1.nhost.run/v1

# Auth Endpoint  
NEXT_PUBLIC_AUTH_ENDPOINT=https://ovceoopmxkuwppiemjjz.auth.ap-south-1.nhost.run/v1

# Storage Endpoint
NEXT_PUBLIC_STORAGE_ENDPOINT=https://ovceoopmxkuwppiemjjz.storage.ap-south-1.nhost.run/v1

# Functions Endpoint
NEXT_PUBLIC_FUNCTIONS_ENDPOINT=https://ovceoopmxkuwppiemjjz.functions.ap-south-1.nhost.run/v1
```

### **2. Install nHost Client SDK**
```bash
cd /Users/<USER>/Desktop/SourceFlex/sourceflex
npm install @nhost/nextjs @nhost/react @nhost/react-apollo
```

### **3. Configure nHost Client**

Create `src/lib/nhost.ts`:
```typescript
import { NhostClient } from '@nhost/nextjs'

export const nhost = new NhostClient({
  subdomain: process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN!,
  region: process.env.NEXT_PUBLIC_NHOST_REGION!,
})
```

### **4. Update Root Layout Provider**

Update `src/providers/providers.tsx`:
```typescript
'use client'

import { NhostProvider } from '@nhost/nextjs'
import { NhostApolloProvider } from '@nhost/react-apollo'
import { nhost } from '@/lib/nhost'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NhostProvider nhost={nhost}>
      <NhostApolloProvider nhost={nhost}>
        {children}
      </NhostApolloProvider>
    </NhostProvider>
  )
}
```

## 🔐 **Authentication Setup**

Your authentication system is already configured with:
- ✅ Email/Password authentication enabled
- ✅ Role-based access control
- ✅ JWT tokens configured
- ✅ User profiles linked to companies and desks

### **Available Auth Roles:**
- `user` - Default role for authenticated users
- `admin` - Administrative privileges  
- `recruiter` - Recruiter-specific permissions
- `manager` - Management-level access

## 🚀 **Next Steps for Development**

### **1. Update Auth Module**
Since the database is ready, update your auth module to use the actual nHost endpoints:

```typescript
// src/modules/auth/hooks/useAuth.ts
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs'

export function useAuth() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus()
  const user = useUserData()
  
  return {
    isAuthenticated,
    isLoading,
    user
  }
}
```

### **2. Test Database Connection**
Let's verify your connection works by testing a simple query:

Would you like me to:
1. Update your auth module to connect to the real nHost database?
2. Test the connection with a sample GraphQL query?
3. Set up the client libraries and test authentication flow?

### **3. Key Business Rules Already Implemented:**
- ✅ **Bench ID System**: Unique candidate identification (FFF+LLL+MMDD+SSSS)
- ✅ **Company Isolation**: Multi-tenant data separation
- ✅ **Desk-based Workflow**: Single desk assignment per user
- ✅ **Role-based Permissions**: Granular access control
- ✅ **Activity Logging**: Full audit trail
- ✅ **Subscription Management**: Billing integration ready

## 🎯 **Database Schema Highlights**

### **Candidates Table** - Ready for Bench Management:
- `bench_id` (unique identifier)
- `ssn_last_four` for uniqueness validation  
- `primary_skills` and `secondary_skills` arrays
- `search_vector` for full-text search
- `document_hashes` for fraud prevention
- Company and desk associations

### **Jobs Table** - Ready for Job Management:
- Full job posting capabilities
- Client associations
- Skill requirements (required/preferred)
- Salary ranges and locations
- Search optimization

### **Advanced Features Already Set Up:**
- **Search Analytics** - Track user search patterns
- **Saved Searches** - User search preferences  
- **Notification System** - Email and in-app notifications
- **Metrics Dashboard** - Ready for analytics
- **File Storage** - Document management with buckets
- **Stripe Integration** - Complete billing system

Your database is enterprise-ready! The schema perfectly matches the SourceFlex requirements from your project overview.

**Would you like me to proceed with setting up the client connection and testing the authentication flow?**

'use client';

export default function OnboardingPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4">
      <div className="max-w-md w-full text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Welcome to SourceFlex
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          Your organization setup is being finalized. Please wait for approval.
        </p>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            Once your organization is approved, you'll have full access to SourceFlex platform features.
          </p>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useAuth, useDeskRouter } from '@/modules/auth';

export default function DashboardPage() {
  const { isLoading, user } = useAuth();
  const { isRouting } = useDeskRouter();

  if (isLoading || isRouting) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Message */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Welcome back, {user?.displayName || 'User'}!
        </h1>
        {user?.company && (
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            {user.company.name} • {user.role?.name} • {user.desk?.name}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Quick Stats */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Active Candidates
          </h3>
          <p className="text-3xl font-bold text-blue-600">0</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Ready to add candidates
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Open Jobs
          </h3>
          <p className="text-3xl font-bold text-green-600">0</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Ready to post jobs
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            This Month's Placements
          </h3>
          <p className="text-3xl font-bold text-purple-600">0</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Start making placements
          </p>
        </div>
      </div>

      {/* Connection Status */}
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
          ✅ Database Connected
        </h2>
        <p className="text-green-700 dark:text-green-300 mb-4">
          Your SourceFlex instance is connected to nHost and ready to use!
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium text-green-800 dark:text-green-200">GraphQL:</span>
            <span className="ml-2 text-green-600 dark:text-green-300">✓ Connected</span>
          </div>
          <div>
            <span className="font-medium text-green-800 dark:text-green-200">Auth:</span>
            <span className="ml-2 text-green-600 dark:text-green-300">✓ Ready</span>
          </div>
          <div>
            <span className="font-medium text-green-800 dark:text-green-200">Storage:</span>
            <span className="ml-2 text-green-600 dark:text-green-300">✓ Available</span>
          </div>
          <div>
            <span className="font-medium text-green-800 dark:text-green-200">Database:</span>
            <span className="ml-2 text-green-600 dark:text-green-300">✓ Schema Ready</span>
          </div>
        </div>
      </div>
    </main>
  );
}

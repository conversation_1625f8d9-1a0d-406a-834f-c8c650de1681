/**
 * Profile Page for Pending Users
 * Shows profile management interface for users awaiting organization approval
 */

import { ProtectedRoute, PendingUserProfile } from '@/modules/auth'

export default function ProfilePage() {
  return (
    <ProtectedRoute 
      requireCompany={false}
      requireApproval={false}
      allowProfileOnly={true}
    >
      <PendingUserProfile />
    </ProtectedRoute>
  )
}
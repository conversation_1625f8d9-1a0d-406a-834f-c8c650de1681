/**
 * API Route for Complete User Deactivation
 * Handles both user_profiles.is_active = false AND auth.users.disabled = true
 */

import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

interface DeactivateUserRequest {
  userId: string
  reason?: string
}

interface DeactivateUserResponse {
  success: boolean
  message: string
  userId?: string
  error?: string
}

export async function POST(request: NextRequest): Promise<NextResponse<DeactivateUserResponse>> {
  try {
    // Parse request body
    const { userId, reason = 'Deactivated by organization admin' }: DeactivateUserRequest = await request.json()

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required', error: 'MISSING_USER_ID' },
        { status: 400 }
      )
    }

    // Get admin secret from environment
    const adminSecret = process.env.NHOST_ADMIN_SECRET
    if (!adminSecret) {
      console.error('Missing NHOST_ADMIN_SECRET environment variable')
      return NextResponse.json(
        { success: false, message: 'Server configuration error', error: 'MISSING_ADMIN_SECRET' },
        { status: 500 }
      )
    }

    // Get the GraphQL endpoint
    const graphqlEndpoint = process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT
    if (!graphqlEndpoint) {
      return NextResponse.json(
        { success: false, message: 'Server configuration error', error: 'MISSING_GRAPHQL_ENDPOINT' },
        { status: 500 }
      )
    }

    // Step 1: Disable user in auth.users table (nHost Admin API)
    const disableAuthUserMutation = `
      mutation DisableAuthUser($userId: uuid!) {
        update_users_by_pk(pk_columns: { id: $userId }, _set: { disabled: true }) {
          id
          disabled
          email
        }
      }
    `

    const authUserResponse = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-hasura-admin-secret': adminSecret,
      },
      body: JSON.stringify({
        query: disableAuthUserMutation,
        variables: { userId }
      })
    })

    const authUserResult = await authUserResponse.json()

    if (authUserResult.errors) {
      console.error('Failed to disable auth user:', authUserResult.errors)
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to disable user authentication', 
          error: 'AUTH_DISABLE_FAILED',
          userId 
        },
        { status: 500 }
      )
    }

    // Step 2: Deactivate user profile
    const deactivateProfileMutation = `
      mutation DeactivateUserProfile($userId: uuid!) {
        update_user_profiles_by_pk(
          pk_columns: { id: $userId }
          _set: { is_active: false, updated_at: "now()" }
        ) {
          id
          is_active
          updated_at
        }
      }
    `

    const profileResponse = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-hasura-admin-secret': adminSecret,
      },
      body: JSON.stringify({
        query: deactivateProfileMutation,
        variables: { userId }
      })
    })

    const profileResult = await profileResponse.json()

    if (profileResult.errors) {
      console.error('Failed to deactivate user profile:', profileResult.errors)
      
      // Attempt to revert auth user disable
      const revertAuthMutation = `
        mutation RevertAuthUser($userId: uuid!) {
          update_users_by_pk(pk_columns: { id: $userId }, _set: { disabled: false }) {
            id
            disabled
          }
        }
      `

      await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-hasura-admin-secret': adminSecret,
        },
        body: JSON.stringify({
          query: revertAuthMutation,
          variables: { userId }
        })
      })

      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to deactivate user profile', 
          error: 'PROFILE_DEACTIVATE_FAILED',
          userId 
        },
        { status: 500 }
      )
    }

    // Step 3: Log the action (optional - for audit trail)
    const logActionMutation = `
      mutation LogUserDeactivation($input: activity_logs_insert_input!) {
        insert_activity_logs_one(object: $input) {
          id
          created_at
        }
      }
    `

    // Get requesting user from headers (if available)
    const requestHeaders = headers()
    const requestingUserId = requestHeaders.get('x-user-id')

    if (requestingUserId) {
      await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-hasura-admin-secret': adminSecret,
        },
        body: JSON.stringify({
          query: logActionMutation,
          variables: {
            input: {
              action: 'USER_DEACTIVATED',
              entity_type: 'user_profile',
              entity_id: userId,
              user_id: requestingUserId,
              description: `User deactivated: ${reason}`,
              metadata: {
                deactivated_user_id: userId,
                reason,
                method: 'admin_api'
              }
            }
          }
        })
      })
    }

    return NextResponse.json({
      success: true,
      message: 'User successfully deactivated',
      userId
    })

  } catch (error) {
    console.error('Error in user deactivation API:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error', 
        error: error instanceof Error ? error.message : 'UNKNOWN_ERROR' 
      },
      { status: 500 }
    )
  }
}
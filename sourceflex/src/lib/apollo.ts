/**
 * Apollo Client Configuration
 * Standalone Apollo client for utility functions and server-side queries
 */

import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Create HTTP link to Hasura GraphQL endpoint
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT || 'https://ovceoopmxkuwppiemjjz.graphql.ap-south-1.nhost.run/v1'
});

// Auth link to add admin secret for server-side queries
const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      ...headers,
      'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET
    }
  };
});

// Create Apollo client for utility functions
export const apolloClient = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'ignore'
    },
    query: {
      errorPolicy: 'all'
    }
  }
});

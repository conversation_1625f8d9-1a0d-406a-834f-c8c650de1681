import { NhostClient } from '@nhost/nextjs'

const nhostSubdomain = process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN;
const nhostRegion = process.env.NEXT_PUBLIC_NHOST_REGION;

if (!nhostSubdomain) {
  throw new Error('NEXT_PUBLIC_NHOST_SUBDOMAIN is not defined');
}
if (!nhostRegion) {
  throw new Error('NEXT_PUBLIC_NHOST_REGION is not defined');
}

export const nhost = new NhostClient({
  subdomain: nhostSubdomain,
  region: nhostRegion,
  
  // Enterprise Session Management Configuration
  auth: {
    // Session persistence: survive browser refresh, clear on browser close
    refreshToken: {
      expiresIn: 60 * 60 * 24 * 30, // 30 days for persistent login
    },
    accessToken: {
      expiresIn: 60 * 15, // 15 minutes for security
    },
    // Client storage configuration
    clientStorage: {
      type: 'cookie', // More secure than localStorage
      prefix: 'sourceflex_auth_', // Namespace cookies
    },
    // Auto refresh tokens before expiry
    autoRefreshToken: true,
    // Sign out inactive sessions
    autoSignIn: true,
  }
})

// Export endpoints for direct access if needed
const graphqlEndpoint = process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT;
if (!graphqlEndpoint) {
  throw new Error('NEXT_PUBLIC_GRAPHQL_ENDPOINT is not defined');
}

const authEndpoint = process.env.NEXT_PUBLIC_AUTH_ENDPOINT;
if (!authEndpoint) {
  throw new Error('NEXT_PUBLIC_AUTH_ENDPOINT is not defined');
}
const storageEndpoint = process.env.NEXT_PUBLIC_STORAGE_ENDPOINT;
if (!storageEndpoint) {
  throw new Error('NEXT_PUBLIC_STORAGE_ENDPOINT is not defined');
}
const functionsEndpoint = process.env.NEXT_PUBLIC_FUNCTIONS_ENDPOINT;
if (!functionsEndpoint) {
  throw new Error('NEXT_PUBLIC_FUNCTIONS_ENDPOINT is not defined');
}

export const endpoints = {
  graphql: graphqlEndpoint,
  auth: authEndpoint,
  storage: storageEndpoint,
  functions: functionsEndpoint,
}

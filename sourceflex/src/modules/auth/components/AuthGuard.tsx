/**
 * Auth Guard Component
 * Handles session validation and redirection for authenticated users
 */

'use client';

import { useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export function AuthGuard({ 
  children, 
  redirectTo = '/dashboard', 
  requireAuth = false 
}: AuthGuardProps) {
  const { isLoading, isAuthenticated } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();

  useEffect(() => {
    // Wait for auth state to load
    if (isLoading) return;

    // Get current pathname to avoid redirect loops
    const currentPath = window.location.pathname;

    // For auth pages (login/register) - redirect if already authenticated
    if (!requireAuth && isAuthenticated && user) {
      // Determine redirect based on user status
      const hasCompany = user.metadata?.companyId;
      const isEmailVerified = user.emailVerified;
      const needsApproval = user.metadata?.registrationStep === 'pending_approval';

      // Avoid redirect loops - don't redirect if already on target page
      if (needsApproval && currentPath !== '/profile') {
        router.replace('/profile');
      } else if (!isEmailVerified && currentPath !== '/verify-email') {
        router.replace('/verify-email');
      } else if (!hasCompany && currentPath !== '/onboarding') {
        router.replace('/onboarding');
      } else if (hasCompany && !['/profile', '/verify-email', '/onboarding'].includes(currentPath)) {
        router.replace(redirectTo);
      }
    }

    // For protected pages - redirect if not authenticated
    if (requireAuth && !isAuthenticated) {
      router.replace('/login');
    }

  }, [isLoading, isAuthenticated, user, router, redirectTo, requireAuth]);

  // Show loading during auth check
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // For auth pages - only show if not authenticated
  if (!requireAuth && isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  // For protected pages - only show if authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return <>{children}</>;
}

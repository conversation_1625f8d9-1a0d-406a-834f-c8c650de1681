/**
 * Protected Route Component
 * Handles authentication and authorization for protected pages
 */

'use client'

import { useAuth } from '../hooks'
import { hasPermission } from '../utils'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { EmailVerificationPrompt } from './EmailVerificationPrompt'

interface ProtectedRouteProps {
  children: React.ReactNode
  requirePermission?: string
  requireCompany?: boolean
  requireApproval?: boolean
  allowProfileOnly?: boolean  // New prop for pending users
}

export function ProtectedRoute({ 
  children, 
  requirePermission,
  requireCompany = true,
  requireApproval = false,
  allowProfileOnly = false
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated, hasCompany, needsApproval, needsEmailVerification } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (isLoading) return

    // Not authenticated - redirect to login
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Needs organization approval - allow profile access if requested
    if (needsApproval && !requireApproval) {
      if (allowProfileOnly) {
        // Allow access to profile-only pages for pending users
        // Component will handle showing appropriate content
      } else {
        router.push('/profile') // Redirect to profile page instead of unauthorized
        return
      }
    }

    // Requires company but user doesn't have one
    if (requireCompany && !hasCompany) {
      router.push('/unauthorized?reason=no_company')
      return
    }

    // Check specific permission if required
    if (requirePermission && user && !hasPermission(user, requirePermission)) {
      router.push('/unauthorized?reason=insufficient_permissions')
      return
    }

  }, [isLoading, isAuthenticated, needsApproval, hasCompany, user, requirePermission, requireCompany, requireApproval, router])

  // Show email verification prompt if needed
  if (needsEmailVerification) {
    return <EmailVerificationPrompt />
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render children until auth check is complete
  if (!isAuthenticated || (requireCompany && !hasCompany)) {
    return null
  }

  // Handle pending approval cases
  if (needsApproval && !requireApproval && !allowProfileOnly) {
    return null
  }

  // Permission check failed
  if (requirePermission && user && !hasPermission(user, requirePermission)) {
    return null
  }

  return <>{children}</>
}

/**
 * Profile-Only Layout for Pending Users
 * Shown to users whose organization approval is pending
 */

'use client'

import React, { useState } from 'react'
import { useAuth } from '../hooks'

interface PendingUserProfileProps {
  children?: React.ReactNode
}

export function PendingUserProfile({ children }: PendingUserProfileProps) {
  const { user, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('status')

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6 px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome to SourceFlex
              </h1>
              <p className="text-gray-600 mt-2">
                Your account is being reviewed
              </p>
            </div>
            <button
              onClick={() => signOut()}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:border-gray-400 transition-colors"
            >
              Sign Out
            </button>
          </div>

          {/* Pending Status Alert */}
          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Organization Approval Pending
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    Your organization registration is currently being reviewed by our team. 
                    This typically takes 24-48 hours. You'll receive an email once your account is approved.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'status', label: 'Approval Status' },
                { id: 'profile', label: 'Profile' },
                { id: 'help', label: 'Help & Support' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'status' && (
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">Approval Status</h3>
              
              <div className="space-y-6">
                {/* Current Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Status
                  </label>
                  <div className="flex items-center space-x-2">
                    <span className="inline-block w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></span>
                    <span className="text-sm font-medium text-yellow-700">PENDING REVIEW</span>
                  </div>
                </div>

                {/* Timeline */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Review Process
                  </label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="text-sm text-gray-600">Account registration completed</div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-sm text-gray-600">Organization verification in progress</div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-gray-300 rounded-full"></div>
                      <div className="text-sm text-gray-400">Account activation (pending)</div>
                    </div>
                  </div>
                </div>

                {/* What happens next */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">What happens next?</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Our team will verify your organization details</li>
                    <li>• You'll receive an email confirmation once approved</li>
                    <li>• Full platform access will be granted immediately</li>
                    <li>• You can start using all SourceFlex features</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">Your Profile</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{user?.email || 'Not available'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Display Name</label>
                  <p className="mt-1 text-sm text-gray-900">{user?.displayName || 'Not set'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Created</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Not available'}
                  </p>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-500">
                    Profile editing will be available once your organization is approved.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'help' && (
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">Help & Support</h3>
              
              <div className="space-y-6">
                {/* FAQ */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Frequently Asked Questions</h4>
                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700">How long does approval take?</h5>
                      <p className="mt-1 text-sm text-gray-600">
                        Most organizations are approved within 24-48 hours during business days.
                      </p>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-gray-700">What if my approval is delayed?</h5>
                      <p className="mt-1 text-sm text-gray-600">
                        If you haven't heard back within 48 hours, please contact our support team.
                      </p>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-gray-700">Can I change my organization details?</h5>
                      <p className="mt-1 text-sm text-gray-600">
                        Contact support if you need to update your organization information during the review process.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Contact Support */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Need Help?</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Contact our support team if you have questions about your account or approval status.
                  </p>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium text-gray-700">Email:</span>{' '}
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                        <EMAIL>
                      </a>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium text-gray-700">Hours:</span>{' '}
                      <span className="text-gray-600">Monday - Friday, 9 AM - 6 PM EST</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Custom children content if provided */}
        {children && (
          <div className="mt-6">
            {children}
          </div>
        )}
      </div>
    </div>
  )
}
'use client';

import { useState } from 'react';
import { useSendVerificationEmail, useSignOut } from '@nhost/nextjs';

export function EmailVerificationPrompt() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const { sendVerificationEmail } = useSendVerificationEmail();
  const { signOut } = useSignOut();

  const handleResendVerification = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await sendVerificationEmail();
      
      if (result.error) {
        setMessage(result.error.message);
      } else {
        setMessage('Verification email sent! Please check your inbox.');
      }
    } catch (error) {
      setMessage('Failed to send verification email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Verify Your Email Address
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
          </p>

          {message && (
            <div className={`mb-6 p-4 rounded-lg ${
              message.includes('sent') 
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300'
                : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300'
            }`}>
              {message}
            </div>
          )}

          <div className="space-y-4">
            <button
              onClick={handleResendVerification}
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </div>
              ) : (
                'Resend Verification Email'
              )}
            </button>

            <button
              onClick={handleSignOut}
              className="w-full py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Sign Out
            </button>
          </div>

          <div className="mt-6 text-xs text-gray-500 dark:text-gray-400">
            <p>Can't find the email? Check your spam folder.</p>
            <p>Still having trouble? Contact support for assistance.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
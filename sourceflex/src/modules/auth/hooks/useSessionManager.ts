/**
 * Session Management Hook
 * Handles enterprise-grade session configuration and cleanup
 */

'use client';

import { useEffect } from 'react';
import { useLogout } from './useLogout';

export interface SessionConfig {
  persistentSession?: boolean; // Keep session across browser restarts
  sessionTimeout?: number; // Auto logout after inactivity (minutes)
  clearOnBrowserClose?: boolean; // Clear session when browser closes
}

export function useSessionManager(config: SessionConfig = {}) {
  const { logout } = useLogout();
  
  const {
    persistentSession = false,
    sessionTimeout = 480, // 8 hours default
    clearOnBrowserClose = true
  } = config;

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Handle browser close behavior
    if (clearOnBrowserClose && !persistentSession) {
      const handleBeforeUnload = () => {
        // Clear session storage when browser closes
        sessionStorage.clear();
        
        // Mark session for cleanup
        localStorage.setItem('sourceflex_session_cleanup', 'true');
      };

      const handlePageShow = (event: PageTransitionEvent) => {
        // Check if returning from cache (browser back/forward)
        if (event.persisted) {
          const needsCleanup = localStorage.getItem('sourceflex_session_cleanup');
          if (needsCleanup) {
            localStorage.removeItem('sourceflex_session_cleanup');
            logout();
          }
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('pageshow', handlePageShow);

      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        window.removeEventListener('pageshow', handlePageShow);
      };
    }
  }, [clearOnBrowserClose, persistentSession, logout]);

  // Auto logout after inactivity
  useEffect(() => {
    if (!sessionTimeout) return;

    let timeoutId: NodeJS.Timeout;
    
    const resetTimeout = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        logout();
      }, sessionTimeout * 60 * 1000); // Convert minutes to milliseconds
    };

    const handleActivity = () => {
      resetTimeout();
    };

    // Monitor user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Set initial timeout
    resetTimeout();

    return () => {
      clearTimeout(timeoutId);
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [sessionTimeout, logout]);

  return {
    isSessionManaged: true
  };
}

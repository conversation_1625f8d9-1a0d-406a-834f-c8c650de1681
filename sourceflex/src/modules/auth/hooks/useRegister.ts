'use client';

/**
 * Registration Hook
 * Handles new user registration and organization detection
 */

import { useSignUpEmailPassword } from '@nhost/nextjs';
import { useMutation } from '@apollo/client';
import { useState } from 'react';
import type { RegisterForm } from '../types';
import { extractDomainFromEmail } from '../utils';
import { detectOrganizationFromDomain } from '../utils/domainDetection';
import { CREATE_ORGANIZATION_APPROVAL_REQUEST } from '../graphql';

export function useRegister() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { signUpEmailPassword } = useSignUpEmailPassword();
  
  const [createApprovalRequest] = useMutation(CREATE_ORGANIZATION_APPROVAL_REQUEST);

  const register = async (data: RegisterForm) => {
    setIsLoading(true);
    setError(null);

    try {
      // Extract domain from email for organization detection
      const domain = extractDomainFromEmail(data.email);
      
      // Check domain status and existing companies
      const domainDetection = await detectOrganizationFromDomain(domain, data.email);
      
      if (domainDetection.isExisting && domainDetection.company) {
        // Domain belongs to existing company - require manual approval
        setError(`Your domain (${domain}) belongs to ${domainDetection.company.name}. Please contact your organization administrator or SourceFlex support.`);
        return { success: false, error: 'Domain belongs to existing organization' };
      }
      
      // Sign up user with nHost
      const result = await signUpEmailPassword(data.email, data.password, {
        displayName: `${data.firstName} ${data.lastName}`,
        metadata: {
          firstName: data.firstName,
          lastName: data.lastName,
          companyName: data.companyName,
          domain,
          registrationStep: 'pending_approval'
        }
      });
      
      if (result.error) {
        setError(result.error.message);
        return { success: false, error: result.error.message };
      }

      // Create organization approval request
      await createApprovalRequest({
        variables: {
          input: {
            company_name: data.companyName,
            domain,
            admin_email: data.email,
            admin_first_name: data.firstName,
            admin_last_name: data.lastName,
            status: 'pending'
          }
        }
      });

      return { success: true, needsApproval: true };
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    register,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}

'use client';

/**
 * Desk Router Hook
 * Handles routing based on user's desk assignment
 */

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from './useAuth';

export function useDeskRouter() {
  const router = useRouter();
  const { user, isLoading, isAuthenticated, needsEmailVerification, needsApproval } = useAuth();

  useEffect(() => {
    // Don't route if still loading or not authenticated
    if (isLoading || !isAuthenticated) return;
    
    // Don't route if needs email verification or approval
    if (needsEmailVerification || needsApproval) return;

    // Don't route if no user data yet
    if (!user) return;

    // Route based on role and desk assignment
    if (user.role?.name === 'platform_admin') {
      router.push('/platform-admin');
    } else if (user.role?.name === 'org_admin') {
      router.push('/dashboard'); // Org admins get dashboard for oversight
    } else if (user.desk?.type === 'recruitment') {
      router.push('/jobs');
    } else if (user.desk?.type === 'bench_sales') {
      router.push('/bench');
    } else {
      router.push('/dashboard'); // Fallback
    }
  }, [user, isLoading, isAuthenticated, needsEmailVerification, needsApproval, router]);

  return {
    isRouting: isLoading || !user,
    currentRoute: getCurrentRoute(user)
  };
}

function getCurrentRoute(user: any): string {
  if (!user) return '/dashboard';
  
  if (user.role?.name === 'platform_admin') {
    return '/platform-admin';
  } else if (user.role?.name === 'org_admin') {
    return '/dashboard';
  } else if (user.desk?.type === 'recruitment') {
    return '/jobs';
  } else if (user.desk?.type === 'bench_sales') {
    return '/bench';
  }
  
  return '/dashboard';
}

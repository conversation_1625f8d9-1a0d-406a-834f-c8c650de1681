'use client';

/**
 * Authentication Hooks
 * React hooks for authentication state management
 */

import { useAuthenticationStatus, useSignOut, useSignInEmailPassword, useUserData } from '@nhost/nextjs';
import { useQuery } from '@apollo/client';
import { useRouter } from 'next/navigation';
import { useState, useMemo } from 'react';
import type { LoginForm, RegisterForm, AuthState, User } from '../types';
import { GET_USER_PROFILE, CHECK_ORGANIZATION_APPROVAL } from '../graphql';

/**
 * Main authentication hook
 * Provides current authentication state and user information
 */
export function useAuth(): AuthState {
  const { isAuthenticated, isLoading: authLoading } = useAuthenticationStatus();
  const nhostUser = useUserData();
  
  // Get user profile data from GraphQL
  const { data: profileData, loading: profileLoading } = useQuery(GET_USER_PROFILE, {
    variables: { userId: nhostUser?.id },
    skip: !nhostUser?.id || !isAuthenticated,
  });

  // Check organization approval status
  const { data: approvalData } = useQuery(CHECK_ORGANIZATION_APPROVAL, {
    variables: { email: nhostUser?.email },
    skip: !nhostUser?.email || !isAuthenticated,
  });

  const user: User | null = useMemo(() => {
    if (!nhostUser || !profileData?.user_profiles_by_pk) {
      return null;
    }

    const profile = profileData.user_profiles_by_pk;
    
    return {
      id: nhostUser.id,
      email: nhostUser.email || '',
      displayName: nhostUser.displayName || '',
      firstName: nhostUser.metadata?.firstName || '',
      lastName: nhostUser.metadata?.lastName || '',
      avatarUrl: nhostUser.avatarUrl || '',
      companyId: profile.company_id,
      roleId: profile.role_id,
      deskId: profile.desk_id,
      isActive: profile.is_active,
      company: profile.company ? {
        id: profile.company.id,
        name: profile.company.name,
        domain: profile.company.domain,
        subscription_status: profile.company.subscription_status,
        subscriptionPlan: profile.company.subscription_plan,
      } : null,
      role: profile.role ? {
        id: profile.role.id,
        name: profile.role.name,
        permissions: profile.role.permissions,
      } : null,
      desk: profile.desk ? {
        id: profile.desk.id,
        name: profile.desk.name,
        type: profile.desk.type,
      } : null,
      settings: profile.settings || {},
    };
  }, [nhostUser, profileData]);

  const needsApproval = useMemo(() => {
    if (!approvalData?.organization_approval_requests?.length) {
      return false;
    }
    
    const request = approvalData.organization_approval_requests[0];
    return request.status === 'pending' || request.status === 'under_review';
  }, [approvalData]);

  // Check if email verification is needed
  const needsEmailVerification = nhostUser && !nhostUser.emailVerified;

  const isLoading = authLoading || profileLoading;

  // Debug logging for post-verification issues
  console.log('useAuth Debug:', {
    isAuthenticated,
    emailVerified: nhostUser?.emailVerified,
    hasProfile: !!profileData?.user_profiles_by_pk,
    needsEmailVerification,
    needsApproval,
    isLoading
  });

  return {
    user,
    isLoading,
    isAuthenticated: !!isAuthenticated && !needsEmailVerification,
    hasCompany: !!user?.companyId,
    hasRole: !!user?.roleId,
    needsApproval,
    needsEmailVerification: !!needsEmailVerification,
  };
}

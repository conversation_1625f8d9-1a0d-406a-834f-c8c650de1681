/**
 * Logout Hook
 * Handles secure session termination
 */

'use client';

import { useSignOut } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export function useLogout() {
  const [isLoading, setIsLoading] = useState(false);
  const { signOut } = useSignOut();
  const router = useRouter();

  const logout = async () => {
    setIsLoading(true);
    
    try {
      // Clear nHost session
      await signOut();
      
      // Clear any local storage data
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
      }
      
      // Redirect to login
      router.push('/login');
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    logout,
    isLoading
  };
}

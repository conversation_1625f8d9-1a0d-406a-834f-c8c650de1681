'use client';

/**
 * Email Verification Completion Hook
 * Handles post-verification workflow including company detection
 */

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUserData } from '@nhost/nextjs';
import { useMutation } from '@apollo/client';
import { extractDomainFromEmail } from '../utils';

// TODO: Add GraphQL mutations for company detection and approval workflow
const CREATE_ORGANIZATION_APPROVAL_REQUEST = `
  mutation CreateOrganizationApprovalRequest($input: organization_approval_requests_insert_input!) {
    insert_organization_approval_requests_one(object: $input) {
      id
      status
    }
  }
`;

export function usePostVerificationHandler() {
  const user = useUserData();
  const router = useRouter();
  const [createApprovalRequest] = useMutation(CREATE_ORGANIZATION_APPROVAL_REQUEST);

  useEffect(() => {
    // Only run if user just got verified and hasn't been processed yet
    if (!user?.emailVerified || user.metadata?.verificationProcessed) {
      return;
    }

    handlePostVerification();
  }, [user?.emailVerified, user?.metadata]);

  const handlePostVerification = async () => {
    if (!user?.email) return;

    try {
      const domain = extractDomainFromEmail(user.email);
      const companyName = user.metadata?.companyName || 'Unknown Company';
      
      // TODO: Check if company already exists by domain
      // const existingCompany = await checkCompanyByDomain(domain);
      
      // For now, create new organization approval request
      await createApprovalRequest({
        variables: {
          input: {
            company_name: companyName,
            domain: domain,
            admin_email: user.email,
            admin_first_name: user.metadata?.firstName || '',
            admin_last_name: user.metadata?.lastName || '',
            status: 'pending',
            is_existing_domain: false, // TODO: Implement domain checking
            created_at: new Date().toISOString()
          }
        }
      });

      // Mark as processed to avoid duplicate requests
      // TODO: Update user metadata to mark as processed
      
      console.log('Organization approval request created for:', companyName);
      
    } catch (error) {
      console.error('Error handling post-verification:', error);
    }
  };

  return {
    isProcessing: user?.emailVerified && !user.metadata?.verificationProcessed
  };
}

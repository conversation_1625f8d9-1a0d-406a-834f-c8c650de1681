'use client';

/**
 * Login Hook
 * Handles user sign in with email and password and desk-based routing
 */

import { useSignInEmailPassword } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import type { LoginForm } from '../types';

export function useLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { signInEmailPassword } = useSignInEmailPassword();
  const router = useRouter();

  const login = async (data: LoginForm) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await signInEmailPassword(data.email, data.password);
      
      if (result.error) {
        setError(result.error.message);
        return { success: false, error: result.error.message };
      }

      // Let the useAuth hook and ProtectedRoute handle routing
      // based on user's desk assignment and email verification status
      router.push('/dashboard');
      return { success: true };
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    login,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}

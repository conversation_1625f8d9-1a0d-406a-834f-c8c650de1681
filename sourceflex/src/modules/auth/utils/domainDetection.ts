/**
 * Domain Detection Utility
 * Handles organization detection from email domains and approval workflow
 */

import { apolloClient } from '@/lib/apollo';
import { CHECK_DOMAIN_COMPANY, CHECK_PENDING_APPROVAL_REQUEST } from '../graphql';
import type { DomainDetectionResult } from '../types';

export interface DomainCheckResult {
  isExisting: boolean;
  company?: {
    id: string;
    name: string;
    domain: string;
    subscription_status: string;
  };
  hasPendingRequest: boolean;
  pendingRequest?: {
    id: string;
    company_name: string;
    status: string;
    created_at: string;
  };
}

/**
 * Check if domain has existing company or pending approval
 */
export async function checkDomainStatus(
  domain: string, 
  email: string
): Promise<DomainCheckResult> {
  try {
    // Check for existing company with this domain
    const companyResult = await apolloClient.query({
      query: CHECK_DOMAIN_COMPANY,
      variables: { domain },
      fetchPolicy: 'network-only'
    });

    const existingCompany = companyResult.data?.companies?.[0];

    // Check for pending approval request
    const pendingResult = await apolloClient.query({
      query: CHECK_PENDING_APPROVAL_REQUEST,
      variables: { domain, email },
      fetchPolicy: 'network-only'
    });

    const pendingRequest = pendingResult.data?.organization_approval_requests?.[0];

    return {
      isExisting: !!existingCompany,
      company: existingCompany,
      hasPendingRequest: !!pendingRequest,
      pendingRequest
    };

  } catch (error) {
    console.error('Domain check failed:', error);
    // Default to requiring approval on error
    return {
      isExisting: false,
      hasPendingRequest: false
    };
  }
}

/**
 * Enhanced domain detection with approval workflow logic
 */
export async function detectOrganizationFromDomain(
  domain: string,
  email: string
): Promise<DomainDetectionResult> {
  
  const domainCheck = await checkDomainStatus(domain, email);

  if (domainCheck.isExisting && domainCheck.company) {
    // Domain has existing company
    return {
      domain,
      isExisting: true,
      company: domainCheck.company,
      canAutoJoin: false, // SourceFlex requires manual approval
      requiresApproval: true
    };
  }

  if (domainCheck.hasPendingRequest) {
    // Domain already has pending approval request
    return {
      domain,
      isExisting: false,
      canAutoJoin: false,
      requiresApproval: true
    };
  }

  // New domain - requires approval request
  return {
    domain,
    isExisting: false,
    canAutoJoin: false,
    requiresApproval: true
  };
}

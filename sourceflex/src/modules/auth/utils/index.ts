/**
 * Authentication Utilities
 * Helper functions for authentication operations
 */

import type { Permission, User, DomainDetectionResult } from '../types';

// Re-export blocked domains utilities
export * from './blockedDomains';

/**
 * Extract domain from email address
 */
export function extractDomainFromEmail(email: string): string {
  const domain = email.split('@')[1];
  if (!domain) {
    throw new Error('Invalid email format');
  }
  return domain.toLowerCase();
}

/**
 * Check if user has specific permission
 */
export function hasPermission(user: User | null, permission: Permission): boolean {
  if (!user?.role?.permissions) {
    return false;
  }

  // Super admin has all permissions
  if (user.role.name === 'Super Admin') {
    return true;
  }

  // Check specific permission
  return user.role.permissions[permission] === true;
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(user, permission));
}

/**
 * Check if user has all specified permissions
 */
export function hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(user, permission));
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Check if domain is commonly used email provider
 */
export function isPublicEmailDomain(domain: string): boolean {
  const publicDomains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'aol.com',
    'icloud.com',
    'protonmail.com',
    'mail.com'
  ];
  
  return publicDomains.includes(domain.toLowerCase());
}

/**
 * Detect organization from email domain
 * TODO: Implement actual domain detection logic
 */
export async function detectOrganizationFromDomain(
  domain: string
): Promise<DomainDetectionResult> {
  // Placeholder implementation
  // In real implementation, this would query the database
  
  return {
    domain,
    isExisting: false,
    canAutoJoin: false,
    requiresApproval: true
  };
}

/**
 * Generate display name from user data
 */
export function getUserDisplayName(user: User | null): string {
  if (!user) return 'Unknown User';
  
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  
  if (user.firstName) {
    return user.firstName;
  }
  
  return user.email;
}

/**
 * Get user initials for avatar
 */
export function getUserInitials(user: User | null): string {
  if (!user) return 'U';
  
  if (user.firstName && user.lastName) {
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  }
  
  if (user.firstName) {
    return user.firstName[0].toUpperCase();
  }
  
  return user.email[0].toUpperCase();
}

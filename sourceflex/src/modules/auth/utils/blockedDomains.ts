/**
 * Blocked Email Domains
 * List of consumer email domains that should be blocked from registration
 */

export const BLOCKED_EMAIL_DOMAINS = [
  // Major consumer email providers
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'icloud.com',
  'aol.com',
  'live.com',
  'msn.com',
  'yahoo.co.uk',
  'yahoo.ca',
  'yahoo.co.jp',
  'gmail.co.uk',
  'hotmail.co.uk',
  'hotmail.ca',
  'hotmail.fr',
  'outlook.co.uk',
  'outlook.ca',
  'outlook.fr',
  
  // Other consumer providers
  'protonmail.com',
  'proton.me',
  'mail.com',
  'zoho.com',
  'yandex.com',
  'mail.ru',
  'qq.com',
  '163.com',
  '126.com',
  'sina.com',
  'sina.cn',
  'sohu.com',
  'tom.com',
  'yeah.net',
  'foxmail.com',
  'aliyun.com',
  'vip.qq.com',
  'rediffmail.com',
  'tutanota.com',
  'tutamail.com',
  'temp-mail.org',
  'guerrillamail.com',
  'mailinator.com',
  '10minutemail.com',
  'sharklasers.com',
  'guerrillamailblock.com',
  'pokemail.net',
  'spam4.me',
  'tempail.com',
  'tempinbox.com',
  
  // Add more as needed
] as const;

/**
 * Check if an email domain is blocked
 */
export function isDomainBlocked(email: string): boolean {
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;
  
  return BLOCKED_EMAIL_DOMAINS.includes(domain as any);
}

/**
 * Get domain from email
 */
export function getEmailDomain(email: string): string | null {
  const domain = email.split('@')[1]?.toLowerCase();
  return domain || null;
}

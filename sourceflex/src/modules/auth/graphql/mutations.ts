/**
 * Authentication GraphQL Mutations
 * Handles organization approval requests and domain detection
 */

import { gql } from '@apollo/client';

export const CHECK_DOMAIN_COMPANY = gql`
  query CheckDomainCompany($domain: String!) {
    companies(where: { domain: { _eq: $domain } }) {
      id
      name
      domain
      subscription_status
      created_at
    }
  }
`;

export const CREATE_ORGANIZATION_APPROVAL_REQUEST = gql`
  mutation CreateOrganizationApprovalRequest($input: organization_approval_requests_insert_input!) {
    insert_organization_approval_requests_one(object: $input) {
      id
      company_name
      domain
      admin_email
      status
      created_at
    }
  }
`;

export const CHECK_PENDING_APPROVAL_REQUEST = gql`
  query CheckPendingApprovalRequest($domain: String!, $email: String!) {
    organization_approval_requests(
      where: { 
        _and: [
          { domain: { _eq: $domain } }
          { admin_email: { _eq: $email } }
          { status: { _eq: "pending" } }
        ]
      }
    ) {
      id
      company_name
      domain
      status
      created_at
    }
  }
`;

export const UPDATE_USER_METADATA = gql`
  mutation UpdateUserMetadata($userId: uuid!, $metadata: jsonb!) {
    updateUser(pk_columns: { id: $userId }, _set: { metadata: $metadata }) {
      id
      metadata
    }
  }
`;

import { gql } from '@apollo/client'

export const GET_USER_PROFILE = gql`
  query GetUserProfile($userId: uuid!) {
    user_profiles_by_pk(id: $userId) {
      id
      is_active
      company_id
      desk_id
      role_id
      settings
      created_at
      updated_at
      company {
        id
        name
        domain
        subscription_status
        subscription_plan {
          id
          name
          features
        }
      }
      desk {
        id
        name
        type
      }
      role {
        id
        name
        permissions
      }
    }
  }
`

export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    users(limit: 1) {
      id
      email
      displayName
      avatarUrl
      defaultRole
      metadata
      disabled
      emailVerified
    }
  }
`

export const CHECK_ORGANIZATION_APPROVAL = gql`
  query CheckOrganizationApproval($email: String!) {
    organization_approval_requests(
      where: { admin_email: { _eq: $email } }
      order_by: { created_at: desc }
      limit: 1
    ) {
      id
      status
      company_name
      approved_at
      review_notes
    }
  }
`

export const GET_USER_PERMISSIONS = gql`
  query GetUserPermissions($userId: uuid!) {
    user_profiles_by_pk(id: $userId) {
      role {
        permissions
      }
      company {
        subscription_plan {
          features
        }
      }
    }
  }
`

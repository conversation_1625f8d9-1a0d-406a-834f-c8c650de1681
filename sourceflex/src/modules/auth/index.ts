/**
 * Authentication Module
 * Entry point for authentication functionality
 */

// Types
export * from './types';

// Hooks
export * from './hooks';

// Utilities
export * from './utils';

// Components
export * from './components';

// Re-export commonly used types for convenience
export type {
  User,
  Company,
  Desk,
  Role,
  LoginForm,
  RegisterForm,
  AuthState,
  Permission,
  DomainDetectionResult
} from './types';

// Re-export commonly used hooks
export {
  useAuth,
  useLogin,
  useLogout,
  useRegister
} from './hooks';

// Re-export commonly used utilities
export {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  isValidEmail,
  validatePassword,
  extractDomainFromEmail,
  getUserDisplayName,
  getUserInitials
} from './utils';

// Module metadata
export const AUTH_MODULE = {
  name: 'auth',
  version: '1.0.0',
  description: 'Authentication and user management module'
};

/**
 * Authentication Module Types
 * Handles user authentication, organization detection, and access control
 */

export interface User {
  id: string;
  email: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  
  // Company and desk assignment
  companyId?: string;
  company?: Company;
  deskId?: string;
  desk?: Desk;
  roleId?: string;
  role?: Role;
  
  // User status
  isActive: boolean;
  settings: Record<string, any>;
}

export interface Company {
  id: string;
  name: string;
  domain?: string;
  subscription_status?: string;
  subscriptionPlan?: SubscriptionPlan;
}

export interface Desk {
  id: string;
  name: string;
  type: 'recruitment' | 'bench_sales';
  description?: string;
  isActive: boolean;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Record<string, boolean>;
  isSystemRole: boolean;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  features: Record<string, boolean>;
  maxUsers?: number;
  maxJobs?: number;
  maxCandidates?: number;
}

// Authentication forms
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  companyName: string;
}

export interface ForgotPasswordForm {
  email: string;
}

export interface ResetPasswordForm {
  password: string;
  confirmPassword: string;
}

// Organization approval request
export interface OrganizationRequest {
  companyName: string;
  domain: string;
  adminEmail: string;
  adminFirstName: string;
  adminLastName: string;
  requestedPlanId: string;
  companySize?: string;
  industry?: string;
  useCase?: string;
}

// Authentication state
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasCompany: boolean;
  hasRole: boolean;
  needsApproval: boolean;
  needsEmailVerification: boolean;
  approvalStatus?: ApprovalStatus;
}

// Approval status enum
export type ApprovalStatus = 'pending' | 'under_review' | 'approved' | 'rejected';

// Domain detection result
export interface DomainDetectionResult {
  domain: string;
  isExisting: boolean;
  company?: Company;
  canAutoJoin: boolean;
  requiresApproval: boolean;
}

// Permission checking
export type Permission = 
  | 'candidates.view'
  | 'candidates.create'
  | 'candidates.edit'
  | 'candidates.delete'
  | 'jobs.view'
  | 'jobs.create'
  | 'jobs.edit'
  | 'jobs.delete'
  | 'submissions.view'
  | 'submissions.create'
  | 'clients.view'
  | 'clients.create'
  | 'clients.edit'
  | 'desk.manage'
  | 'company.admin';

export interface PermissionCheck {
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
}

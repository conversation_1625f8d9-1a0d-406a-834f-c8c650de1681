/**
 * Billing Module Types
 * Stripe integration and subscription management
 */

// =============================================================================
// STRIPE INTEGRATION TYPES
// =============================================================================

export interface StripeCustomer {
  id: string;
  companyId: string;
  stripeCustomerId: string;
  email: string;
  name?: string;
  phone?: string;
  address?: StripeAddress;
  createdAt: Date;
  updatedAt: Date;
}

export interface StripeAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export interface StripeSubscription {
  id: string;
  companyId: string;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  subscriptionPlanId?: string;
  subscriptionPlan?: SubscriptionPlan;
  status: StripeSubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  quantity: number;
  trialStart?: Date;
  trialEnd?: Date;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface StripeInvoice {
  id: string;
  companyId: string;
  stripeCustomerId: string;
  stripeInvoiceId: string;
  stripeSubscriptionId?: string;
  amountDue: number; // in cents
  amountPaid: number; // in cents
  currency: string;
  status: StripeInvoiceStatus;
  dueDate?: Date;
  paidAt?: Date;
  hostedInvoiceUrl?: string;
  invoicePdf?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface StripeWebhookEvent {
  id: string;
  stripeEventId: string;
  eventType: string;
  data: Record<string, any>;
  processed: boolean;
  processedAt?: Date;
  processingAttempts: number;
  lastError?: string;
  createdAt: Date;
}

// =============================================================================
// SUBSCRIPTION PLAN TYPES
// =============================================================================

export interface SubscriptionPlan {
  id: string;
  name: string;
  priceMonthly: number;
  priceYearly: number;
  maxUsers?: number;
  maxJobs?: number;
  maxCandidates?: number;
  features: SubscriptionFeatures;
  isActive: boolean;
  stripeProductId?: string;
  stripePriceIdMonthly?: string;
  stripePriceIdYearly?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubscriptionFeatures {
  // Core features
  semanticSearch: boolean;
  documentManagement: boolean;
  basicAnalytics: boolean;
  
  // Communication
  emailSupport: boolean;
  phoneSupport?: boolean;
  prioritySupport?: boolean;
  
  // Advanced features
  advancedAnalytics?: boolean;
  customAnalytics?: boolean;
  apiAccess?: boolean;
  customIntegrations?: boolean;
  bulkOperations?: boolean;
  
  // Enterprise features
  whiteLabel?: boolean;
  dedicatedAccountManager?: boolean;
  customBranding?: boolean;
  ssoIntegration?: boolean;
}

// =============================================================================
// BILLING TYPES
// =============================================================================

export interface BillingInfo {
  companyId: string;
  stripeCustomerId?: string;
  currentSubscription?: StripeSubscription;
  paymentMethods: StripePaymentMethod[];
  billingAddress?: StripeAddress;
  taxId?: string;
  billingEmail: string;
  autoCollection: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StripePaymentMethod {
  id: string;
  type: 'card' | 'bank_account' | 'sepa_debit';
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  bankAccount?: {
    accountHolderType: string;
    bankName: string;
    last4: string;
    routingNumber: string;
  };
  isDefault: boolean;
  createdAt: Date;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitAmount: number; // in cents
  totalAmount: number; // in cents
  periodStart?: Date;
  periodEnd?: Date;
  metadata?: Record<string, any>;
}

export interface UsageRecord {
  id: string;
  companyId: string;
  subscriptionId: string;
  metricType: 'users' | 'jobs' | 'candidates' | 'api_calls';
  quantity: number;
  timestamp: Date;
  recorded: boolean;
  stripeUsageRecordId?: string;
}

// =============================================================================
// ENUMS
// =============================================================================

export type StripeSubscriptionStatus = 
  | 'incomplete'
  | 'incomplete_expired'
  | 'trialing'
  | 'active'
  | 'past_due'
  | 'canceled'
  | 'unpaid';

export type StripeInvoiceStatus = 
  | 'draft'
  | 'open'
  | 'paid'
  | 'void'
  | 'uncollectible';

export type BillingCycle = 'monthly' | 'yearly';

export type PlanTier = 'starter' | 'professional' | 'enterprise';

// =============================================================================
// FORM TYPES
// =============================================================================

export interface CreateSubscriptionForm {
  planId: string;
  billingCycle: BillingCycle;
  paymentMethodId?: string;
  promotionCode?: string;
  quantity?: number;
  trialDays?: number;
}

export interface UpdateSubscriptionForm {
  planId?: string;
  quantity?: number;
  promotionCode?: string;
  cancelAtPeriodEnd?: boolean;
}

export interface UpdateBillingInfoForm {
  billingEmail?: string;
  billingAddress?: StripeAddress;
  taxId?: string;
  autoCollection?: boolean;
}

export interface AddPaymentMethodForm {
  paymentMethodId: string;
  setAsDefault?: boolean;
}

// =============================================================================
// DASHBOARD TYPES
// =============================================================================

export interface BillingDashboard {
  currentSubscription?: StripeSubscription;
  currentPlan?: SubscriptionPlan;
  nextInvoice?: StripeInvoice;
  recentInvoices: StripeInvoice[];
  paymentMethods: StripePaymentMethod[];
  usageMetrics: UsageMetrics;
  billingInfo: BillingInfo;
}

export interface UsageMetrics {
  users: {
    current: number;
    limit?: number;
    percentage: number;
  };
  jobs: {
    current: number;
    limit?: number;
    percentage: number;
  };
  candidates: {
    current: number;
    limit?: number;
    percentage: number;
  };
  apiCalls?: {
    current: number;
    limit?: number;
    percentage: number;
  };
}

export interface PlanComparison {
  plans: SubscriptionPlan[];
  currentPlanId?: string;
  recommendedPlanId?: string;
  savings?: {
    monthly: number;
    yearly: number;
  };
}

export interface BillingHistory {
  invoices: StripeInvoice[];
  totalSpent: number;
  averageMonthlySpend: number;
  paymentFailures: number;
  subscriptionChanges: SubscriptionChange[];
}

export interface SubscriptionChange {
  id: string;
  changeType: 'upgrade' | 'downgrade' | 'cancel' | 'reactivate';
  fromPlan?: string;
  toPlan?: string;
  effectiveDate: Date;
  reason?: string;
  amount?: number;
}

// =============================================================================
// WEBHOOK TYPES
// =============================================================================

export interface StripeWebhookData {
  eventType: string;
  data: {
    object: any;
    previous_attributes?: any;
  };
  created: number;
  livemode: boolean;
}

export interface WebhookProcessingResult {
  success: boolean;
  message: string;
  actions: string[];
  errors?: string[];
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface CreateSubscriptionResponse {
  success: boolean;
  subscription?: StripeSubscription;
  clientSecret?: string; // For 3D Secure authentication
  requires3DSecure: boolean;
  message: string;
}

export interface UpdateSubscriptionResponse {
  success: boolean;
  subscription?: StripeSubscription;
  prorationAmount?: number;
  effectiveDate?: Date;
  message: string;
}

export interface CancelSubscriptionResponse {
  success: boolean;
  subscription?: StripeSubscription;
  refundAmount?: number;
  effectiveDate: Date;
  message: string;
}

export interface ProcessPaymentResponse {
  success: boolean;
  paymentIntentId?: string;
  clientSecret?: string;
  requires3DSecure: boolean;
  message: string;
}

// =============================================================================
// PRICING TYPES
// =============================================================================

export interface PricingCalculation {
  basePrice: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  billingCycle: BillingCycle;
  prorationCredit?: number;
  nextBillingDate: Date;
}

export interface PricingProration {
  currentPeriodUsage: number;
  newPeriodCost: number;
  creditAmount: number;
  chargeAmount: number;
  effectiveDate: Date;
}

export interface DiscountCode {
  id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  maxUses?: number;
  usedCount: number;
  validFrom: Date;
  validTo?: Date;
  applicablePlans?: string[];
  isActive: boolean;
}

/**
 * Organization Administration Permission Utils
 * Helper functions for checking org admin permissions
 */

import type { User } from '@/modules/auth/types'
import type { OrgAdminPermission } from '../types'

/**
 * Check if user can manage users in their organization
 */
export function canManageUsers(user: User | null): boolean {
  if (!user?.role?.permissions) return false
  
  return !!(
    user.role.permissions['company.admin'] ||
    user.role.permissions['users.manage'] ||
    user.role.permissions['organization.admin']
  )
}

/**
 * Check if user has specific org admin permission
 */
export function hasOrgAdminPermission(
  user: User | null, 
  permission: OrgAdminPermission
): boolean {
  if (!user?.role?.permissions) return false
  
  // Super admin can do anything
  if (user.role.permissions['company.admin']) return true
  
  // Check specific permission
  return !!user.role.permissions[permission]
}

/**
 * Check if user can invite new users
 */
export function canInviteUsers(user: User | null): boolean {
  return hasOrgAdminPermission(user, 'users.invite')
}

/**
 * Check if user can edit other users
 */
export function canEditUsers(user: User | null): boolean {
  return hasOrgAdminPermission(user, 'users.edit')
}

/**
 * Check if user can approve user requests
 */
export function canApproveUsers(user: User | null): boolean {
  return hasOrgAdminPermission(user, 'users.approve')
}

/**
 * Check if user can edit company settings
 */
export function canEditCompany(user: User | null): boolean {
  return hasOrgAdminPermission(user, 'company.edit')
}

/**
 * Check if user can manage desks
 */
export function canManageDesks(user: User | null): boolean {
  return !!(
    hasOrgAdminPermission(user, 'desks.create') ||
    hasOrgAdminPermission(user, 'desks.edit') ||
    hasOrgAdminPermission(user, 'desks.delete')
  )
}

/**
 * Get list of permissions user has for org admin
 */
export function getOrgAdminPermissions(user: User | null): OrgAdminPermission[] {
  if (!user?.role?.permissions) return []
  
  const permissions: OrgAdminPermission[] = []
  const userPerms = user.role.permissions
  
  // Map user permissions to org admin permissions
  const permissionMap: Record<string, OrgAdminPermission[]> = {
    'company.admin': [
      'users.invite', 'users.edit', 'users.deactivate', 'users.approve',
      'company.edit', 'desks.create', 'desks.edit', 'desks.delete',
      'settings.edit', 'billing.view', 'billing.edit'
    ],
    'users.invite': ['users.invite'],
    'users.edit': ['users.edit'],
    'users.deactivate': ['users.deactivate'],
    'users.approve': ['users.approve'],
    'company.edit': ['company.edit'],
    'desks.create': ['desks.create'],
    'desks.edit': ['desks.edit'],
    'desks.delete': ['desks.delete'],
    'settings.edit': ['settings.edit'],
    'billing.view': ['billing.view'],
    'billing.edit': ['billing.edit']
  }
  
  Object.keys(userPerms).forEach(perm => {
    if (userPerms[perm] && permissionMap[perm]) {
      permissions.push(...permissionMap[perm])
    }
  })
  
  // Remove duplicates
  return [...new Set(permissions)]
}

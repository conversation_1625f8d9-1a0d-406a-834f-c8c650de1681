/**
 * Domain validation utilities for organization admin
 */

/**
 * Extract domain from email address
 */
export function extractDomain(email: string): string {
  const match = email.match(/@(.+)$/);
  return match ? match[1].toLowerCase() : '';
}

/**
 * Validate if domain is a valid company domain
 * Excludes common public email providers
 */
export function validateCompanyDomain(domain: string): {
  isValid: boolean;
  isPublicDomain: boolean;
  reason?: string;
} {
  if (!domain) {
    return { isValid: false, isPublicDomain: false, reason: 'Domain is required' };
  }

  // Common public email domains to exclude
  const publicDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'aol.com', 'icloud.com', 'live.com', 'msn.com',
    'mail.com', 'zoho.com', 'protonmail.com', 'yandex.com'
  ];

  const normalizedDomain = domain.toLowerCase();

  if (publicDomains.includes(normalizedDomain)) {
    return {
      isValid: false,
      isPublicDomain: true,
      reason: 'Public email domains are not allowed for company registration'
    };
  }

  // Basic domain format validation
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  if (!domainRegex.test(normalizedDomain)) {
    return {
      isValid: false,
      isPublicDomain: false,
      reason: 'Invalid domain format'
    };
  }

  return { isValid: true, isPublicDomain: false };
}

/**
 * Check if two domains match (handles subdomains)
 */
export function domainsMatch(domain1: string, domain2: string): boolean {
  const normalize = (d: string) => d.toLowerCase().replace(/^www\./, '');
  return normalize(domain1) === normalize(domain2);
}

/**
 * Get company domain suggestions based on email
 */
export function suggestCompanyDomain(email: string): string[] {
  const domain = extractDomain(email);
  if (!domain) return [];

  const suggestions = [domain];
  
  // Remove common prefixes
  if (domain.startsWith('mail.')) {
    suggestions.push(domain.replace('mail.', ''));
  }
  
  // Add www version
  if (!domain.startsWith('www.')) {
    suggestions.push(`www.${domain}`);
  }

  return [...new Set(suggestions)];
}

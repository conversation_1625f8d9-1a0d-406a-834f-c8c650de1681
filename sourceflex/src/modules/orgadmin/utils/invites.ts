/**
 * User invitation utilities for organization admin
 */

import { v4 as uuidv4 } from 'uuid'

/**
 * Generate secure invite link for new users
 */
export function generateInviteLink(
  companyId: string,
  invitedByUserId: string,
  email: string,
  roleId?: string,
  deskId?: string
): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  const inviteToken = generateInviteToken()
  
  const params = new URLSearchParams({
    token: inviteToken,
    email,
    company: companyId,
    ...(roleId && { role: roleId }),
    ...(deskId && { desk: deskId })
  })

  return `${baseUrl}/invite?${params.toString()}`
}

/**
 * Generate secure invite token
 */
function generateInviteToken(): string {
  return uuidv4().replace(/-/g, '')
}

/**
 * Validate invite link format
 */
export function validateInviteLink(link: string): {
  isValid: boolean
  params?: {
    token: string
    email: string
    companyId: string
    roleId?: string
    deskId?: string
  }
} {
  try {
    const url = new URL(link)
    
    if (!url.pathname.includes('/invite')) {
      return { isValid: false }
    }

    const token = url.searchParams.get('token')
    const email = url.searchParams.get('email')
    const companyId = url.searchParams.get('company')
    
    if (!token || !email || !companyId) {
      return { isValid: false }
    }

    return {
      isValid: true,
      params: {
        token,
        email,
        companyId,
        roleId: url.searchParams.get('role') || undefined,
        deskId: url.searchParams.get('desk') || undefined
      }
    }
  } catch {
    return { isValid: false }
  }
}

/**
 * Generate welcome email content for invited users
 */
export function generateWelcomeEmailContent(
  inviterName: string,
  companyName: string,
  inviteLink: string,
  roleName?: string
): {
  subject: string
  htmlContent: string
  textContent: string
} {
  const subject = `You're invited to join ${companyName} on SourceFlex`
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Welcome to SourceFlex!</h2>
      
      <p>Hi there,</p>
      
      <p><strong>${inviterName}</strong> has invited you to join <strong>${companyName}</strong> on SourceFlex.</p>
      
      ${roleName ? `<p>You'll be joining as a <strong>${roleName}</strong>.</p>` : ''}
      
      <div style="margin: 30px 0;">
        <a href="${inviteLink}" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Accept Invitation
        </a>
      </div>
      
      <p>This invitation will expire in 7 days.</p>
      
      <p>If you have any questions, please contact your administrator.</p>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="color: #6b7280; font-size: 14px;">
        This email was sent by SourceFlex. If you weren't expecting this invitation, you can safely ignore this email.
      </p>
    </div>
  `
  
  const textContent = `
Welcome to SourceFlex!

Hi there,

${inviterName} has invited you to join ${companyName} on SourceFlex.

${roleName ? `You'll be joining as a ${roleName}.` : ''}

To accept your invitation, visit: ${inviteLink}

This invitation will expire in 7 days.

If you have any questions, please contact your administrator.

---
This email was sent by SourceFlex. If you weren't expecting this invitation, you can safely ignore this email.
  `

  return { subject, htmlContent, textContent }
}

/**
 * Hook for managing user approval workflow
 */

import { useCallback, useState } from 'react'
import { usePendingUsers } from './usePendingUsers'
import { generateInviteLink, generateWelcomeEmailContent } from '../utils'
import { useAuth } from '@/modules/auth'
import type { UserApprovalRequest } from '../types'

export function useUserApproval() {
  const { user } = useAuth()
  const { pendingUsers, approveUser, rejectUser, isApproving, isRejecting } = usePendingUsers()
  const [processingStates, setProcessingStates] = useState<Record<string, boolean>>({})

  const handleApproval = useCallback(async (
    request: UserApprovalRequest,
    options: {
      roleId?: string
      deskId?: string
      sendWelcomeEmail?: boolean
      reviewNotes?: string
    } = {}
  ) => {
    if (!user?.companyId) throw new Error('No company context')

    setProcessingStates(prev => ({ ...prev, [request.id]: true }))

    try {
      // Approve the request
      await approveUser(request.id, options.reviewNotes)

      // Generate invite link if user approval is for existing company
      if (request.requestType === 'existing_company' && options.sendWelcomeEmail) {
        const inviteLink = generateInviteLink(
          user.companyId,
          user.id,
          request.email,
          options.roleId,
          options.deskId
        )

        // Generate welcome email content
        const emailContent = generateWelcomeEmailContent(
          user.displayName || `${user.firstName} ${user.lastName}`,
          user.company?.name || 'Your Organization',
          inviteLink
        )

        // TODO: Send email via communication module
        console.log('Welcome email content:', emailContent)
      }

      return { success: true }
    } catch (error) {
      console.error('Error approving user:', error)
      return { success: false, error }
    } finally {
      setProcessingStates(prev => ({ ...prev, [request.id]: false }))
    }
  }, [user, approveUser])

  const handleRejection = useCallback(async (
    requestId: string,
    rejectionReason: string
  ) => {
    setProcessingStates(prev => ({ ...prev, [requestId]: true }))

    try {
      await rejectUser(requestId, rejectionReason)
      return { success: true }
    } catch (error) {
      console.error('Error rejecting user:', error)
      return { success: false, error }
    } finally {
      setProcessingStates(prev => ({ ...prev, [requestId]: false }))
    }
  }, [rejectUser])

  const bulkApprove = useCallback(async (
    requests: UserApprovalRequest[],
    options: {
      defaultRoleId?: string
      defaultDeskId?: string
      sendWelcomeEmails?: boolean
    } = {}
  ) => {
    const results = []

    for (const request of requests) {
      const result = await handleApproval(request, {
        roleId: options.defaultRoleId,
        deskId: options.defaultDeskId,
        sendWelcomeEmail: options.sendWelcomeEmails,
        reviewNotes: 'Bulk approval'
      })
      results.push({ requestId: request.id, ...result })
    }

    return results
  }, [handleApproval])

  const bulkReject = useCallback(async (
    requestIds: string[],
    rejectionReason: string
  ) => {
    const results = []

    for (const requestId of requestIds) {
      const result = await handleRejection(requestId, rejectionReason)
      results.push({ requestId, ...result })
    }

    return results
  }, [handleRejection])

  return {
    pendingRequests: pendingUsers,
    handleApproval,
    handleRejection,
    bulkApprove,
    bulkReject,
    isProcessing: (requestId: string) => processingStates[requestId] || false,
    isApproving,
    isRejecting
  }
}

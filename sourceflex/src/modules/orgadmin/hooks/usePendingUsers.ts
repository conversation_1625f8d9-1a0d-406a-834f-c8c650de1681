/**
 * Hook for managing pending user approval requests
 */

import { useQuery, useMutation, useQueryClient } from '@apollo/client'
import { useCallback } from 'react'
import { useAuth } from '@/modules/auth'
import { 
  GET_PENDING_USERS,
  APPROVE_USER_REQUEST,
  REJECT_USER_REQUEST
} from '../graphql'
import type { PendingUser } from '../types'

export function usePendingUsers() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Get pending user requests for the organization
  const { 
    data, 
    loading, 
    error, 
    refetch 
  } = useQuery(GET_PENDING_USERS, {
    variables: { domain: user?.company?.domain },
    skip: !user?.company?.domain,
  })

  // Approve user request mutation
  const [approveUserMutation, { loading: approving }] = useMutation(APPROVE_USER_REQUEST, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetPendingUsers' })
      queryClient.invalidateQueries({ queryName: 'GetOrganizationUsers' })
    }
  })

  // Reject user request mutation
  const [rejectUserMutation, { loading: rejecting }] = useMutation(REJECT_USER_REQUEST, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetPendingUsers' })
    }
  })

  const approveUser = useCallback(async (requestId: string, reviewNotes?: string) => {
    const result = await approveUserMutation({
      variables: {
        requestId,
        reviewNotes
      }
    })

    return result.data?.update_organization_approval_requests_by_pk
  }, [approveUserMutation])

  const rejectUser = useCallback(async (requestId: string, reviewNotes: string) => {
    const result = await rejectUserMutation({
      variables: {
        requestId,
        reviewNotes
      }
    })

    return result.data?.update_organization_approval_requests_by_pk
  }, [rejectUserMutation])

  const pendingUsers: PendingUser[] = data?.organization_approval_requests?.map((request: any) => ({
    id: request.id,
    email: request.admin_email,
    firstName: request.admin_first_name,
    lastName: request.admin_last_name,
    companyId: user?.companyId || '',
    requestedAt: new Date(request.created_at),
    status: request.status,
    company: {
      id: user?.companyId || '',
      name: request.company_name,
      domain: request.domain
    }
  })) || []

  return {
    pendingUsers,
    loading,
    error,
    refetch,
    approveUser,
    rejectUser,
    isApproving: approving,
    isRejecting: rejecting
  }
}

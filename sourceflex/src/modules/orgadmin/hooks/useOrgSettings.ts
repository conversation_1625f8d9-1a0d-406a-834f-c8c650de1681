/**
 * Hook for managing organization settings and profile
 */

import { useQuery, useMutation, useQueryClient } from '@apollo/client'
import { useCallback } from 'react'
import { useAuth } from '@/modules/auth'
import { 
  GET_ORGANIZATION_PROFILE,
  UPDATE_COMPANY_PROFILE,
  CREATE_DESK,
  UPDATE_DESK,
  DELETE_DESK
} from '../graphql'
import type { 
  OrganizationSettings, 
  CompanyProfile, 
  UpdateCompanyForm,
  CreateDeskForm 
} from '../types'

export function useOrgSettings() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Get organization profile and settings
  const { 
    data, 
    loading, 
    error, 
    refetch 
  } = useQuery(GET_ORGANIZATION_PROFILE, {
    variables: { companyId: user?.companyId },
    skip: !user?.companyId,
  })

  // Update company profile mutation
  const [updateCompanyMutation, { loading: updating }] = useMutation(UPDATE_COMPANY_PROFILE, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationProfile' })
    }
  })

  // Desk management mutations
  const [createDeskMutation, { loading: creatingDesk }] = useMutation(CREATE_DESK, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationProfile' })
    }
  })

  const [updateDeskMutation, { loading: updatingDesk }] = useMutation(UPDATE_DESK, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationProfile' })
    }
  })

  const [deleteDeskMutation, { loading: deletingDesk }] = useMutation(DELETE_DESK, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationProfile' })
    }
  })

  const updateCompanyProfile = useCallback(async (updates: UpdateCompanyForm) => {
    if (!user?.companyId) throw new Error('No company context')

    const result = await updateCompanyMutation({
      variables: {
        companyId: user.companyId,
        updates: {
          ...(updates.name && { name: updates.name }),
          ...(updates.website && { website: updates.website }),
          ...(updates.industry && { industry: updates.industry }),
          ...(updates.sizeRange && { size_range: updates.sizeRange }),
          ...(updates.logoUrl && { logo_url: updates.logoUrl })
        }
      }
    })

    return result.data?.update_companies_by_pk
  }, [user?.companyId, updateCompanyMutation])

  const createDesk = useCallback(async (form: CreateDeskForm) => {
    if (!user?.companyId) throw new Error('No company context')

    const result = await createDeskMutation({
      variables: {
        input: {
          company_id: user.companyId,
          name: form.name,
          type: form.type,
          description: form.description,
          is_active: true
        }
      }
    })

    return result.data?.insert_desks_one
  }, [user?.companyId, createDeskMutation])

  const updateDesk = useCallback(async (deskId: string, updates: Partial<CreateDeskForm>) => {
    const result = await updateDeskMutation({
      variables: {
        deskId,
        updates: {
          ...(updates.name && { name: updates.name }),
          ...(updates.type && { type: updates.type }),
          ...(updates.description !== undefined && { description: updates.description })
        }
      }
    })

    return result.data?.update_desks_by_pk
  }, [updateDeskMutation])

  const deleteDesk = useCallback(async (deskId: string) => {
    const result = await deleteDeskMutation({
      variables: { deskId }
    })

    return result.data?.update_desks_by_pk
  }, [deleteDeskMutation])

  const organization: CompanyProfile | null = data?.companies_by_pk || null

  return {
    organization,
    loading,
    error,
    refetch,
    updateCompanyProfile,
    createDesk,
    updateDesk,
    deleteDesk,
    isUpdating: updating,
    isCreatingDesk: creatingDesk,
    isUpdatingDesk: updatingDesk,
    isDeletingDesk: deletingDesk
  }
}

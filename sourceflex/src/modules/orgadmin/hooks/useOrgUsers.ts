/**
 * Hook for managing organization users
 */

import { useQuery, useMutation, useQueryClient } from '@apollo/client'
import { useCallback } from 'react'
import { useAuth } from '@/modules/auth'
import { 
  GET_ORGANIZATION_USERS,
  CREATE_USER_PROFILE,
  UPDATE_USER_PROFILE
} from '../graphql'
import type { OrgUser, InviteUserForm, UpdateUserForm } from '../types'

export function useOrgUsers() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Get all users in the organization
  const { 
    data, 
    loading, 
    error, 
    refetch 
  } = useQuery(GET_ORGANIZATION_USERS, {
    variables: { companyId: user?.companyId },
    skip: !user?.companyId,
  })

  // Create new user profile mutation
  const [createUserProfileMutation, { loading: creating }] = useMutation(CREATE_USER_PROFILE, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationUsers' })
    }
  })

  // Update user profile mutation
  const [updateUserProfileMutation, { loading: updating }] = useMutation(UPDATE_USER_PROFILE, {
    onCompleted: () => {
      queryClient.invalidateQueries({ queryName: 'GetOrganizationUsers' })
    }
  })

  const createUserProfile = useCallback(async (form: InviteUserForm) => {
    if (!user?.companyId) throw new Error('No company context')

    const result = await createUserProfileMutation({
      variables: {
        input: {
          company_id: user.companyId,
          role_id: form.roleId,
          desk_id: form.deskId || null,
          is_active: true,
          settings: {}
        }
      }
    })

    return result.data?.insert_user_profiles_one
  }, [user?.companyId, createUserProfileMutation])

  const updateUser = useCallback(async (userId: string, updates: UpdateUserForm) => {
    const result = await updateUserProfileMutation({
      variables: {
        userId,
        updates: {
          ...(updates.roleId && { role_id: updates.roleId }),
          ...(updates.deskId !== undefined && { desk_id: updates.deskId }),
          ...(updates.isActive !== undefined && { is_active: updates.isActive })
        }
      }
    })

    return result.data?.update_user_profiles_by_pk
  }, [updateUserProfileMutation])

  const deactivateUser = useCallback(async (userId: string, reason?: string) => {
    try {
      // Call our complete deactivation API route
      const response = await fetch('/api/admin/deactivate-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, reason })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to deactivate user')
      }

      // Refresh the user list after successful deactivation
      await refetch()

      return { success: true, userId }
    } catch (error) {
      console.error('Failed to deactivate user:', error)
      throw error
    }
  }, [refetch])

  const users: OrgUser[] = data?.user_profiles?.map((profile: any) => ({
    id: profile.id,
    email: '', // Will need to get from auth.users
    firstName: '', // Will need to get from auth.users  
    lastName: '', // Will need to get from auth.users
    displayName: '',
    avatarUrl: '',
    companyId: profile.company_id,
    deskId: profile.desk_id,
    roleId: profile.role_id,
    isActive: profile.is_active,
    lastLoginAt: undefined,
    createdAt: new Date(profile.created_at),
    updatedAt: new Date(profile.updated_at),
    desk: profile.desk,
    role: profile.role,
    timezone: '',
    preferences: profile.settings || {}
  })) || []

  return {
    users,
    loading,
    error,
    refetch,
    createUserProfile,
    updateUser,
    deactivateUser,
    isCreating: creating,
    isUpdating: updating
  }
}

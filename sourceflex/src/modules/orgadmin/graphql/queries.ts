import { gql } from '@apollo/client'

// Get all users in the organization with auth data
export const GET_ORGANIZATION_USERS = gql`
  query GetOrganizationUsers($companyId: uuid!) {
    user_profiles(
      where: { company_id: { _eq: $companyId }, is_active: { _eq: true } }
      order_by: { created_at: desc }
    ) {
      id
      is_active
      created_at
      updated_at
      company_id
      desk_id
      role_id
      settings
      desk {
        id
        name
        type
      }
      role {
        id
        name
        permissions
      }
    }
  }
`

// Get organization profile and settings
export const GET_ORGANIZATION_PROFILE = gql`
  query GetOrganizationProfile($companyId: uuid!) {
    companies_by_pk(id: $companyId) {
      id
      name
      domain
      website
      industry
      size_range
      logo_url
      subscription_status
      subscription_expires_at
      current_users
      current_jobs
      current_candidates
      settings
      created_at
      updated_at
      subscription_plan {
        id
        name
        max_users
        max_jobs
        max_candidates
        features
      }
      desks {
        id
        name
        type
        is_active
        created_at
        user_profiles_aggregate {
          aggregate {
            count
          }
        }
      }
    }
  }
`

// Get organization roles for user assignment
export const GET_ORGANIZATION_ROLES = gql`
  query GetOrganizationRoles {
    roles(where: { is_system_role: { _eq: false } }) {
      id
      name
      description
      permissions
    }
  }
`

// Mutations for user profile management
export const CREATE_USER_PROFILE = gql`
  mutation CreateUserProfile($input: user_profiles_insert_input!) {
    insert_user_profiles_one(object: $input) {
      id
      company_id
      role_id
      desk_id
      is_active
    }
  }
`

export const UPDATE_USER_PROFILE = gql`
  mutation UpdateUserProfile($userId: uuid!, $updates: user_profiles_set_input!) {
    update_user_profiles_by_pk(pk_columns: { id: $userId }, _set: $updates) {
      id
      is_active
      desk_id
      role_id
    }
  }
`

export const DEACTIVATE_USER_PROFILE = gql`
  mutation DeactivateUserProfile($userId: uuid!) {
    update_user_profiles_by_pk(
      pk_columns: { id: $userId }
      _set: { is_active: false }
    ) {
      id
      is_active
    }
  }
`

// Company/organization updates
export const UPDATE_COMPANY_PROFILE = gql`
  mutation UpdateCompanyProfile($companyId: uuid!, $updates: companies_set_input!) {
    update_companies_by_pk(pk_columns: { id: $companyId }, _set: $updates) {
      id
      name
      website
      industry
      size_range
      logo_url
      updated_at
    }
  }
`

// Desk management
export const CREATE_DESK = gql`
  mutation CreateDesk($input: desks_insert_input!) {
    insert_desks_one(object: $input) {
      id
      name
      type
      description
      is_active
    }
  }
`

export const UPDATE_DESK = gql`
  mutation UpdateDesk($deskId: uuid!, $updates: desks_set_input!) {
    update_desks_by_pk(pk_columns: { id: $deskId }, _set: $updates) {
      id
      name
      type
      description
      is_active
    }
  }
`

export const DELETE_DESK = gql`
  mutation DeleteDesk($deskId: uuid!) {
    update_desks_by_pk(
      pk_columns: { id: $deskId }
      _set: { is_active: false }
    ) {
      id
      is_active
    }
  }
`

// User approval workflow
export const APPROVE_USER_REQUEST = gql`
  mutation ApproveUserRequest($requestId: uuid!, $reviewNotes: String) {
    update_organization_approval_requests_by_pk(
      pk_columns: { id: $requestId }
      _set: { 
        status: "approved"
        approved_at: "now()"
        review_notes: $reviewNotes
      }
    ) {
      id
      status
      approved_at
    }
  }
`

export const REJECT_USER_REQUEST = gql`
  mutation RejectUserRequest($requestId: uuid!, $reviewNotes: String!) {
    update_organization_approval_requests_by_pk(
      pk_columns: { id: $requestId }
      _set: { 
        status: "rejected"
        review_notes: $reviewNotes
      }
    ) {
      id
      status
      review_notes
    }
  }
`

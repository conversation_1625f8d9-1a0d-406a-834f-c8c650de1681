/**
 * Organization Profile Component
 * Manages company profile information and branding using real database data
 */

'use client'

import React, { useState } from 'react'
import { useOrgSettings } from '../hooks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Building2, Globe, Users, Calendar, Edit, Save, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export function OrganizationProfile() {
  const { organization, loading, error, updateCompanyProfile, isUpdating } = useOrgSettings()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    website: '',
    industry: '',
    sizeRange: ''
  })

  // Initialize form data when organization loads
  React.useEffect(() => {
    if (organization) {
      setFormData({
        name: organization.name || '',
        website: organization.website || '',
        industry: organization.industry || '',
        sizeRange: organization.size_range || ''
      })
    }
  }, [organization])

  const handleSave = async () => {
    try {
      await updateCompanyProfile({
        name: formData.name,
        website: formData.website,
        industry: formData.industry,
        sizeRange: formData.sizeRange
      })
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update profile:', error)
    }
  }

  const handleCancel = () => {
    if (organization) {
      setFormData({
        name: organization.name || '',
        website: organization.website || '',
        industry: organization.industry || '',
        sizeRange: organization.size_range || ''
      })
    }
    setIsEditing(false)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Organization Profile...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Profile</CardTitle>
          <CardDescription>
            Failed to load organization profile. Please refresh the page.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (!organization) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Organization Not Found</CardTitle>
          <CardDescription>
            No organization data found. Please contact support.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Company Profile */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="w-5 h-5" />
                Company Profile
              </CardTitle>
              <CardDescription>
                Manage your organization's public information and branding
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isUpdating}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isUpdating}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isUpdating ? 'Saving...' : 'Save'}
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {isEditing ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="company-name">Company Name</Label>
                <Input
                  id="company-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter company name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                  placeholder="https://company.com"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="industry">Industry</Label>
                <Input
                  id="industry"
                  value={formData.industry}
                  onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                  placeholder="e.g., Technology, Healthcare"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="size-range">Company Size</Label>
                <Select
                  value={formData.sizeRange}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, sizeRange: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 employees</SelectItem>
                    <SelectItem value="11-50">11-50 employees</SelectItem>
                    <SelectItem value="51-200">51-200 employees</SelectItem>
                    <SelectItem value="201-500">201-500 employees</SelectItem>
                    <SelectItem value="500+">500+ employees</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-sm font-medium text-gray-500">Company Name</Label>
                <p className="text-lg font-medium">{organization.name}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Domain</Label>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {organization.domain || 'Not set'}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Website</Label>
                {organization.website ? (
                  <a 
                    href={organization.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 flex items-center gap-1"
                  >
                    <Globe className="w-4 h-4" />
                    {organization.website}
                  </a>
                ) : (
                  <p className="text-gray-500">Not set</p>
                )}
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Industry</Label>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {organization.industry || 'Not specified'}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Company Size</Label>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {organization.size_range || 'Not specified'}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Member Since</Label>
                <p className="text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {new Date(organization.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Subscription Info */}
      <Card>
        <CardHeader>
          <CardTitle>Subscription & Usage</CardTitle>
          <CardDescription>
            Current plan and usage statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {organization.current_users || 0}
              </div>
              <div className="text-sm text-gray-500">Active Users</div>
              {organization.subscription_plan?.max_users && (
                <div className="text-xs text-gray-400">
                  of {organization.subscription_plan.max_users}
                </div>
              )}
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {organization.current_jobs || 0}
              </div>
              <div className="text-sm text-gray-500">Active Jobs</div>
              {organization.subscription_plan?.max_jobs && (
                <div className="text-xs text-gray-400">
                  of {organization.subscription_plan.max_jobs}
                </div>
              )}
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {organization.current_candidates || 0}
              </div>
              <div className="text-sm text-gray-500">Candidates</div>
              {organization.subscription_plan?.max_candidates && (
                <div className="text-xs text-gray-400">
                  of {organization.subscription_plan.max_candidates}
                </div>
              )}
            </div>
            
            <div className="text-center">
              <Badge 
                variant={organization.subscription_status === 'active' ? 'default' : 'secondary'}
                className="text-sm"
              >
                {organization.subscription_status?.toUpperCase() || 'TRIAL'}
              </Badge>
              <div className="text-sm text-gray-500 mt-1">Status</div>
              {organization.subscription_expires_at && (
                <div className="text-xs text-gray-400">
                  Expires {new Date(organization.subscription_expires_at).toLocaleDateString()}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Desks Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Desks Overview</CardTitle>
          <CardDescription>
            Organization desks and their current usage
          </CardDescription>
        </CardHeader>
        <CardContent>
          {organization.desks && organization.desks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {organization.desks.map((desk) => (
                <Card key={desk.id} className="bg-gray-50 dark:bg-gray-800">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{desk.name}</h4>
                      <Badge variant={desk.is_active ? 'default' : 'secondary'}>
                        {desk.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        {desk.user_profiles_aggregate?.aggregate?.count || 0} users
                      </div>
                      <div className="capitalize">
                        {desk.type.replace('_', ' ')}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No desks configured. Set up your first desk to organize your team.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

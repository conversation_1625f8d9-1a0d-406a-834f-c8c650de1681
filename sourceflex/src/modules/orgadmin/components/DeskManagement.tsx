/**
 * Desk Management Component
 * Manages organization desks with real database data
 */

'use client'

import React, { useState } from 'react'
import { useOrgSettings } from '../hooks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Briefcase, Plus, Edit, Users, Calendar } from 'lucide-react'

export function DeskManagement() {
  const { organization, loading, error, createDesk, updateDesk, deleteDesk, isCreatingDesk } = useOrgSettings()
  const [showCreateForm, setShowCreateForm] = useState(false)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Desks...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Desks</CardTitle>
          <CardDescription>
            Failed to load organization desks. Please try again.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const desks = organization?.desks || []

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Desk Management
              </CardTitle>
              <CardDescription>
                Organize your team into desks for recruitment and bench sales activities
              </CardDescription>
            </div>
            <Button 
              onClick={() => setShowCreateForm(true)}
              disabled={isCreatingDesk}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create Desk
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Desks Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Desk Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {desks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    <Briefcase className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No desks configured</p>
                    <p className="text-sm">Create your first desk to organize your team</p>
                  </TableCell>
                </TableRow>
              ) : (
                desks.map((desk) => (
                  <TableRow key={desk.id}>
                    <TableCell>
                      <div className="font-medium">{desk.name}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {desk.type.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4 text-gray-400" />
                        {desk.user_profiles_aggregate?.aggregate?.count || 0}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={desk.is_active ? 'default' : 'secondary'}>
                        {desk.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {new Date(desk.created_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* TODO: Add CreateDeskDialog component */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Create New Desk</CardTitle>
              <CardDescription>
                Create desk form will be implemented next
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => setShowCreateForm(false)}>
                Close
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

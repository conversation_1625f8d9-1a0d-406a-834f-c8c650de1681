/**
 * Organization Administration Layout
 * Main layout for organization admin pages with navigation
 */

'use client'

import React from 'react'
import { useAuth } from '@/modules/auth'
import { canManageUsers } from '../utils'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { UserManagement } from './UserManagement'
import { OrganizationProfile } from './OrganizationProfile'
import { DeskManagement } from './DeskManagement'
import { UserApprovalWorkflow } from './UserApprovalWorkflow'
import { CompanySettings } from './CompanySettings'
import { 
  Users, 
  Building2, 
  Settings, 
  UserCheck, 
  Briefcase 
} from 'lucide-react'

interface OrgAdminLayoutProps {
  children?: React.ReactNode
  defaultTab?: string
}

export function OrgAdminLayout({ children, defaultTab = 'users' }: OrgAdminLayoutProps) {
  const { user } = useAuth()

  // Check if user has org admin permissions
  if (!canManageUsers(user)) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle>Access Restricted</CardTitle>
            <CardDescription>
              You don't have permission to access organization administration.
              Contact your administrator for access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto py-6 px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Organization Administration
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2">
                Manage users, settings, and organization configuration
              </p>
            </div>
            <Badge variant="secondary" className="text-sm">
              {user?.company?.name}
            </Badge>
          </div>
        </div>

        {/* Content */}
        {children || (
          <Tabs defaultValue={defaultTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Users
              </TabsTrigger>
              <TabsTrigger value="approval" className="flex items-center gap-2">
                <UserCheck className="w-4 h-4" />
                Approvals
              </TabsTrigger>
              <TabsTrigger value="desks" className="flex items-center gap-2">
                <Briefcase className="w-4 h-4" />
                Desks
              </TabsTrigger>
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <Building2 className="w-4 h-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="users">
                <UserManagement />
              </TabsContent>

              <TabsContent value="approval">
                <UserApprovalWorkflow />
              </TabsContent>

              <TabsContent value="desks">
                <DeskManagement />
              </TabsContent>

              <TabsContent value="profile">
                <OrganizationProfile />
              </TabsContent>

              <TabsContent value="settings">
                <CompanySettings />
              </TabsContent>
            </div>
          </Tabs>
        )}
      </div>
    </div>
  )
}

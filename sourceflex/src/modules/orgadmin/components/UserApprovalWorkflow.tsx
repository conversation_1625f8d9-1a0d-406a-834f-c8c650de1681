/**
 * User Approval Workflow Component
 * Manages pending user approval requests with real database data
 */

'use client'

import React from 'react'
import { usePendingUsers } from '../hooks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { UserCheck, UserX, Clock } from 'lucide-react'

export function UserApprovalWorkflow() {
  const { pendingUsers, loading, error } = usePendingUsers()

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Pending Requests...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Requests</CardTitle>
          <CardDescription>
            Failed to load pending user requests. Please try again.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Pending User Approvals
        </CardTitle>
        <CardDescription>
          Review and approve users requesting access to your organization
        </CardDescription>
      </CardHeader>
      <CardContent>
        {pendingUsers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No pending approval requests</p>
            <p className="text-sm">New user requests will appear here for review</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Request Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pendingUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {user.firstName} {user.lastName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {user.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.company.name}</div>
                      <div className="text-sm text-gray-500">{user.company.domain}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {user.requestedAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      user.status === 'pending' ? 'secondary' : 
                      user.status === 'approved' ? 'default' : 'destructive'
                    }>
                      {user.status.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm" className="text-green-600">
                        <UserCheck className="w-4 h-4 mr-2" />
                        Approve
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600">
                        <UserX className="w-4 h-4 mr-2" />
                        Reject
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Company Settings Component
 * Manages organization-wide settings and preferences with real database data
 */

'use client'

import React, { useState } from 'react'
import { useOrgSettings } from '../hooks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Settings, Bell, Shield, CreditCard, Save } from 'lucide-react'

export function CompanySettings() {
  const { organization, loading, error, updateCompanyProfile, isUpdating } = useOrgSettings()
  const [settings, setSettings] = useState({
    autoApproveUsers: false,
    requireEmailVerification: true,
    allowGuestAccess: false,
    emailNotifications: {
      newUserRequests: true,
      systemUpdates: true,
      securityAlerts: true
    }
  })

  // Initialize settings from organization data
  React.useEffect(() => {
    if (organization?.settings) {
      setSettings(prev => ({
        ...prev,
        ...organization.settings
      }))
    }
  }, [organization])

  const handleSaveSettings = async () => {
    try {
      // TODO: Implement settings update
      console.log('Saving settings:', settings)
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Settings...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Settings</CardTitle>
          <CardDescription>
            Failed to load organization settings. Please try again.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* User Management Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            User Management
          </CardTitle>
          <CardDescription>
            Configure how users join and access your organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-approve">Auto-approve Users</Label>
              <p className="text-sm text-gray-500">
                Automatically approve users from your domain
              </p>
            </div>
            <Switch
              id="auto-approve"
              checked={settings.autoApproveUsers}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ ...prev, autoApproveUsers: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-verification">Require Email Verification</Label>
              <p className="text-sm text-gray-500">
                Users must verify their email before accessing the platform
              </p>
            </div>
            <Switch
              id="email-verification"
              checked={settings.requireEmailVerification}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ ...prev, requireEmailVerification: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="guest-access">Allow Guest Access</Label>
              <p className="text-sm text-gray-500">
                Allow limited access for external collaborators
              </p>
            </div>
            <Switch
              id="guest-access"
              checked={settings.allowGuestAccess}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ ...prev, allowGuestAccess: checked }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Choose which email notifications you want to receive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="new-user-notifications">New User Requests</Label>
              <p className="text-sm text-gray-500">
                Get notified when users request access
              </p>
            </div>
            <Switch
              id="new-user-notifications"
              checked={settings.emailNotifications.newUserRequests}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ 
                  ...prev, 
                  emailNotifications: { 
                    ...prev.emailNotifications, 
                    newUserRequests: checked 
                  }
                }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="system-updates">System Updates</Label>
              <p className="text-sm text-gray-500">
                Get notified about platform updates and maintenance
              </p>
            </div>
            <Switch
              id="system-updates"
              checked={settings.emailNotifications.systemUpdates}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ 
                  ...prev, 
                  emailNotifications: { 
                    ...prev.emailNotifications, 
                    systemUpdates: checked 
                  }
                }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="security-alerts">Security Alerts</Label>
              <p className="text-sm text-gray-500">
                Get notified about security-related events
              </p>
            </div>
            <Switch
              id="security-alerts"
              checked={settings.emailNotifications.securityAlerts}
              onCheckedChange={(checked) => 
                setSettings(prev => ({ 
                  ...prev, 
                  emailNotifications: { 
                    ...prev.emailNotifications, 
                    securityAlerts: checked 
                  }
                }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Subscription Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Subscription & Billing
          </CardTitle>
          <CardDescription>
            Manage your subscription plan and billing information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <div className="font-medium">
                {organization?.subscription_plan?.name || 'Free Trial'}
              </div>
              <div className="text-sm text-gray-500">
                Current plan
              </div>
            </div>
            <div className="text-right">
              <Badge variant={organization?.subscription_status === 'active' ? 'default' : 'secondary'}>
                {organization?.subscription_status?.toUpperCase() || 'TRIAL'}
              </Badge>
              {organization?.subscription_expires_at && (
                <div className="text-xs text-gray-500 mt-1">
                  Expires {new Date(organization.subscription_expires_at).toLocaleDateString()}
                </div>
              )}
            </div>
          </div>
          
          <div className="mt-4">
            <Button variant="outline" className="w-full">
              Manage Billing
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Save Settings */}
      <Card>
        <CardContent className="pt-6">
          <Button 
            onClick={handleSaveSettings}
            disabled={isUpdating}
            className="w-full"
          >
            <Save className="w-4 h-4 mr-2" />
            {isUpdating ? 'Saving...' : 'Save Settings'}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

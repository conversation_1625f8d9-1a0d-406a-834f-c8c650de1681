/**
 * Organization Administration Types
 * Types for organization-level user management and settings
 */

export interface OrgUser {
  id: string
  email: string
  firstName: string
  lastName: string
  displayName: string
  avatarUrl?: string
  
  // Organization context
  companyId: string
  deskId?: string
  roleId: string
  
  // Status and activity
  isActive: boolean
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
  
  // Related entities
  desk?: {
    id: string
    name: string
    type: 'recruitment' | 'bench_sales'
  }
  role?: {
    id: string
    name: string
    permissions: Record<string, boolean>
  }
  
  // User preferences
  timezone?: string
  preferences: Record<string, any>
}

export interface PendingUser {
  id: string
  email: string
  firstName: string
  lastName: string
  companyId: string
  requestedAt: Date
  status: 'pending' | 'approved' | 'rejected'
  approvedBy?: string
  approvedAt?: Date
  rejectionReason?: string
  
  // Company context
  company: {
    id: string
    name: string
    domain: string
  }
}

export interface UserApprovalRequest {
  id: string
  email: string
  firstName: string
  lastName: string
  companyName: string
  domain: string
  requestType: 'new_company' | 'existing_company'
  status: 'pending' | 'approved' | 'rejected' | 'under_review'
  
  // Request details
  companySize?: string
  industry?: string
  useCase?: string
  requestedPlanId?: string
  
  // Approval workflow
  reviewedBy?: string
  reviewNotes?: string
  approvedAt?: Date
  createdAt: Date
  
  // Detection results
  isExistingDomain: boolean
  duplicateCheckResults: Record<string, any>
}

export interface OrganizationSettings {
  companyId: string
  
  // Company profile
  companyName: string
  domain?: string
  website?: string
  industry?: string
  sizeRange?: string
  logoUrl?: string
  
  // User management settings
  autoApproveUsers: boolean
  requireEmailVerification: boolean
  allowGuestAccess: boolean
  sessionTimeout: number
  
  // Notification preferences
  emailNotifications: {
    newUserRequests: boolean
    systemUpdates: boolean
    securityAlerts: boolean
  }
  
  // Feature flags
  features: Record<string, boolean>
  
  // Subscription context
  subscriptionPlan?: {
    id: string
    name: string
    maxUsers?: number
    features: Record<string, boolean>
  }
  
  updatedAt: Date
  updatedBy: string
}

export interface DeskConfig {
  id: string
  companyId: string
  name: string
  type: 'recruitment' | 'bench_sales'
  description?: string
  isActive: boolean
  
  // Desk settings
  settings: {
    defaultTimezone?: string
    autoAssignCandidates?: boolean
    enableBenchTracking?: boolean
    requireClientApproval?: boolean
  }
  
  // Usage stats
  userCount: number
  activeUserCount: number
  
  createdAt: Date
  updatedAt: Date
}

export interface CompanyProfile {
  id: string
  name: string
  domain?: string
  website?: string
  industry?: string
  sizeRange?: string
  logoUrl?: string
  
  // Subscription info
  subscriptionStatus: 'trial' | 'active' | 'suspended' | 'cancelled'
  subscriptionExpiresAt?: Date
  currentUsers: number
  currentJobs: number
  currentCandidates: number
  
  // Desk configuration
  desks: DeskConfig[]
  
  settings: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// Form types for organization admin operations
export interface InviteUserForm {
  email: string
  firstName: string
  lastName: string
  roleId: string
  deskId?: string
  sendWelcomeEmail: boolean
}

export interface UpdateUserForm {
  firstName?: string
  lastName?: string
  roleId?: string
  deskId?: string
  isActive?: boolean
  timezone?: string
}

export interface UpdateCompanyForm {
  name?: string
  website?: string
  industry?: string
  sizeRange?: string
  logoUrl?: string
}

export interface CreateDeskForm {
  name: string
  type: 'recruitment' | 'bench_sales'
  description?: string
  settings: Record<string, any>
}

// Permission helpers
export type OrgAdminPermission = 
  | 'users.invite'
  | 'users.edit'
  | 'users.deactivate'
  | 'users.approve'
  | 'company.edit'
  | 'desks.create'
  | 'desks.edit'
  | 'desks.delete'
  | 'settings.edit'
  | 'billing.view'
  | 'billing.edit'

/**
 * Organization Administration Module
 * Handles organization-level settings, user management, and admin workflows
 * Accessible only to organization administrators
 */

// Components
export { OrgAdminLayout } from './components/OrgAdminLayout'
export { UserManagement } from './components/UserManagement'
export { OrganizationProfile } from './components/OrganizationProfile'
export { DeskManagement } from './components/DeskManagement'
export { UserApprovalWorkflow } from './components/UserApprovalWorkflow'
export { CompanySettings } from './components/CompanySettings'

// Hooks
export { useOrgUsers } from './hooks/useOrgUsers'
export { useOrgSettings } from './hooks/useOrgSettings'
export { usePendingUsers } from './hooks/usePendingUsers'
export { useUserApproval } from './hooks/useUserApproval'

// Types
export type {
  OrgUser,
  PendingUser,
  OrganizationSettings,
  UserApprovalRequest,
  DeskConfig,
  CompanyProfile
} from './types'

// Utilities
export { canManageUsers } from './utils/permissions'
export { generateInviteLink } from './utils/invites'
export { validateCompanyDomain } from './utils/domain'

/**
 * Complete ApprovalQueue Component
 * Platform admin interface for organization approval workflow
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger 
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Eye, Check, X, Clock, Building2, User } from 'lucide-react';
import { usePendingApprovals, useCompleteOrganizationSetup } from '../hooks';
import { useReviewApproval } from '../hooks/useApprovals';

interface ApprovalDetailsDialogProps {
  approval: any;
  onApprove: (approval: any, notes?: string) => void;
  onReject: (approval: any, notes: string) => void;
}

function ApprovalDetailsDialog({ approval, onApprove, onReject }: ApprovalDetailsDialogProps) {
  const [notes, setNotes] = useState('');
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);
  const [processing, setProcessing] = useState(false);

  const handleSubmit = async () => {
    if (!action) return;
    
    setProcessing(true);
    try {
      if (action === 'approve') {
        await onApprove(approval, notes);
      } else {
        await onReject(approval, notes || 'Request rejected');
      }
    } finally {
      setProcessing(false);
      setAction(null);
      setNotes('');
    }
  };

  return (
    <DialogContent className="max-w-2xl">
      <DialogHeader>
        <DialogTitle>Organization Approval Request</DialogTitle>
      </DialogHeader>
      
      <div className="space-y-6">
        {/* Company Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold text-sm text-gray-500 mb-1">Company Name</h3>
            <p className="font-medium">{approval.company_name}</p>
          </div>
          <div>
            <h3 className="font-semibold text-sm text-gray-500 mb-1">Domain</h3>
            <p className="font-mono text-sm">{approval.domain}</p>
          </div>
        </div>

        {/* Admin Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold text-sm text-gray-500 mb-1">Admin Name</h3>
            <p>{approval.admin_first_name} {approval.admin_last_name}</p>
          </div>
          <div>
            <h3 className="font-semibold text-sm text-gray-500 mb-1">Email</h3>
            <p className="text-sm">{approval.admin_email}</p>
          </div>
        </div>

        {/* Additional Information */}
        {(approval.company_size || approval.industry || approval.use_case) && (
          <div className="space-y-3">
            <h3 className="font-semibold">Additional Information</h3>
            {approval.company_size && (
              <div>
                <span className="text-sm font-medium text-gray-500">Company Size: </span>
                <span>{approval.company_size}</span>
              </div>
            )}
            {approval.industry && (
              <div>
                <span className="text-sm font-medium text-gray-500">Industry: </span>
                <span>{approval.industry}</span>
              </div>
            )}
            {approval.use_case && (
              <div>
                <span className="text-sm font-medium text-gray-500">Use Case: </span>
                <p className="mt-1 text-sm">{approval.use_case}</p>
              </div>
            )}
          </div>
        )}

        {/* Request Date */}
        <div>
          <h3 className="font-semibold text-sm text-gray-500 mb-1">Requested</h3>
          <p className="text-sm">{new Date(approval.created_at).toLocaleString()}</p>
        </div>

        {/* Review Notes */}
        <div>
          <h3 className="font-semibold mb-2">Review Notes (Optional)</h3>
          <Textarea
            placeholder="Add any notes about this approval decision..."
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        {!action && (
          <div className="flex gap-3 pt-4 border-t">
            <Button
              onClick={() => setAction('approve')}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <Check className="w-4 h-4 mr-2" />
              Approve & Create Organization
            </Button>
            <Button
              onClick={() => setAction('reject')}
              variant="destructive"
              className="flex-1"
            >
              <X className="w-4 h-4 mr-2" />
              Reject Request
            </Button>
          </div>
        )}

        {action && (
          <div className="bg-gray-50 p-4 rounded-lg border-t">
            <div className="flex items-center justify-between mb-4">
              <p className="font-medium">
                {action === 'approve' 
                  ? '✅ Creating complete organization setup...' 
                  : '❌ Rejecting this request...'
                }
              </p>
              {!processing && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setAction(null)}
                >
                  Cancel
                </Button>
              )}
            </div>
            <Button 
              onClick={handleSubmit} 
              className="w-full"
              disabled={processing || (action === 'reject' && !notes.trim())}
            >
              {processing ? 'Processing...' : `Confirm ${action === 'approve' ? 'Approval' : 'Rejection'}`}
            </Button>
          </div>
        )}
      </div>
    </DialogContent>
  );
}

export function ApprovalQueue() {
  const [currentPage, setCurrentPage] = useState(1);
  const { approvals, totalCount, loading, error, refetch } = usePendingApprovals(currentPage, 10);
  const { reviewApproval } = useReviewApproval();
  const { completeApproval } = useCompleteOrganizationSetup();

  const handleApprove = async (approval: any, notes?: string) => {
    const result = await completeApproval(approval, notes);
    if (result.success) {
      refetch();
    }
  };

  const handleReject = async (approval: any, notes: string) => {
    const result = await reviewApproval(approval.id, { action: 'reject', notes });
    if (result.success) {
      refetch();
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Organization Approval Queue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Organization Approval Queue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-red-600">Failed to load approval requests</p>
            <Button onClick={() => refetch()} className="mt-4">Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Organization Approval Queue
          </div>
          <Badge variant="secondary">{totalCount} pending</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {approvals.length === 0 ? (
          <div className="text-center p-8 text-gray-500">
            <Check className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="font-semibold mb-2">All caught up!</h3>
            <p>No pending organization approval requests</p>
          </div>
        ) : (
          <div className="space-y-4">
            {approvals.map((approval) => (
              <div key={approval.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Building2 className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold">{approval.company_name}</h3>
                      <Badge variant="outline">{approval.domain}</Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <User className="w-4 h-4" />
                      <span>{approval.admin_first_name} {approval.admin_last_name}</span>
                      <span>•</span>
                      <span>{approval.admin_email}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Requested {new Date(approval.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4 mr-1" />
                          Review
                        </Button>
                      </DialogTrigger>
                      <ApprovalDetailsDialog
                        approval={approval}
                        onApprove={handleApprove}
                        onReject={handleReject}
                      />
                    </Dialog>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Platform Admin Types
 * Type definitions for organization approval workflow
 */

// Organization approval request from database
export interface OrganizationApprovalRequest {
  id: string;
  company_name: string;
  domain: string;
  admin_email: string;
  admin_first_name: string;
  admin_last_name: string;
  status: 'pending' | 'approved' | 'rejected' | 'review';
  created_at: string;
  updated_at?: string;
  company_size?: string;
  industry?: string;
  use_case?: string;
  review_notes?: string;
  reviewed_by?: string;
  approved_at?: string;
  is_existing_domain?: boolean;
  duplicate_check_results?: Record<string, any>;
}

// Form data for reviewing an approval request
export interface ReviewApprovalForm {
  action: 'approve' | 'reject';
  notes?: string;
}

// Approval queue filters
export interface ApprovalFilters {
  status?: string;
  domain?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Basic company info
export interface CompanyInfo {
  id: string;
  name: string;
  domain?: string;
  created_at: string;
}

// User profile for company admin
export interface UserProfileInfo {
  user_id: string;
  company_id: string;
  role_id: string;
}

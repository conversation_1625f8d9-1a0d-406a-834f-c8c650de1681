/**
 * Simple Platform Admin Hooks
 * Basic hooks for organization approval workflow
 */

import { useQuery, useMutation } from '@apollo/client';
import { 
  GET_PENDING_APPROVALS, 
  GET_APPROVAL_REQUEST_DETAILS,
  APPROVE_ORGANIZATION_REQUEST,
  REJECT_ORGANIZATION_REQUEST
} from '../graphql';
import type { OrganizationApprovalRequest, ReviewApprovalForm } from '../types';

/**
 * Hook to get pending organization approval requests
 */
export function usePendingApprovals(page = 1, pageSize = 20) {
  const offset = (page - 1) * pageSize;
  
  const { data, loading, error, refetch } = useQuery(GET_PENDING_APPROVALS, {
    variables: { limit: pageSize, offset },
    fetchPolicy: 'cache-and-network'
  });

  return {
    approvals: data?.organization_approval_requests || [],
    totalCount: data?.organization_approval_requests_aggregate?.aggregate?.count || 0,
    loading,
    error,
    refetch
  };
}

/**
 * Hook to get specific approval request details
 */
export function useApprovalDetails(id: string) {
  const { data, loading, error, refetch } = useQuery(GET_APPROVAL_REQUEST_DETAILS, {
    variables: { id },
    skip: !id
  });

  return {
    approval: data?.organization_approval_requests_by_pk,
    loading,
    error,
    refetch
  };
}

/**
 * Hook for reviewing (approve/reject) organization requests
 */
export function useReviewApproval() {
  const [approveRequest] = useMutation(APPROVE_ORGANIZATION_REQUEST);
  const [rejectRequest] = useMutation(REJECT_ORGANIZATION_REQUEST);

  const reviewApproval = async (id: string, reviewForm: ReviewApprovalForm) => {
    try {
      if (reviewForm.action === 'approve') {
        const result = await approveRequest({
          variables: { 
            id, 
            notes: reviewForm.notes || null 
          }
        });
        return { success: true, data: result.data };
      } else {
        const result = await rejectRequest({
          variables: { 
            id, 
            notes: reviewForm.notes || 'Request rejected' 
          }
        });
        return { success: true, data: result.data };
      }
    } catch (error) {
      console.error('Error reviewing approval:', error);
      return { success: false, error };
    }
  };

  return {
    reviewApproval
  };
}

/**
 * Complete Organization Setup Hook
 * Handles full organization creation workflow after approval
 */

import { useMutation, useQuery, useLazyQuery } from '@apollo/client';
import { 
  COMPLETE_ORGANIZATION_SETUP,
  ASSIGN_USER_TO_COMPANY,
  GET_ORG_ADMIN_ROLE,
  GET_USER_BY_EMAIL,
  GET_DEFAULT_SUBSCRIPTION_PLAN 
} from '../graphql';

export function useCompleteOrganizationSetup() {
  const [setupOrganization] = useMutation(COMPLETE_ORGANIZATION_SETUP);
  const [assignUser] = useMutation(ASSIGN_USER_TO_COMPANY);
  const [getUserByEmail] = useLazyQuery(GET_USER_BY_EMAIL);
  
  const { data: roleData } = useQuery(GET_ORG_ADMIN_ROLE);
  const { data: planData } = useQuery(GET_DEFAULT_SUBSCRIPTION_PLAN);

  const completeApproval = async (approvalRequest: any, reviewNotes?: string) => {
    try {
      const orgAdminRole = roleData?.roles?.[0];
      const defaultPlan = planData?.subscription_plans?.[0];
      
      if (!orgAdminRole) {
        throw new Error('Organization admin role not found');
      }

      // Step 1: Get user by email
      const userResult = await getUserByEmail({
        variables: { email: approvalRequest.admin_email }
      });
      
      const user = userResult.data?.users?.[0];
      if (!user) {
        throw new Error('User not found for email: ' + approvalRequest.admin_email);
      }

      // Step 2: Create company and desk
      const setupResult = await setupOrganization({
        variables: {
          approvalId: approvalRequest.id,
          companyData: {
            name: approvalRequest.company_name,
            domain: approvalRequest.domain,
            subscription_plan_id: defaultPlan?.id,
            subscription_status: 'trial',
            approval_request_id: approvalRequest.id
          },
          deskData: {
            company_id: null, // Will be set by database trigger
            name: 'General',
            type: 'recruitment',
            is_active: true
          },
          reviewNotes
        }
      });

      const company = setupResult.data?.insert_companies_one;
      const desk = setupResult.data?.insert_desks_one;
      
      if (!company || !desk) {
        throw new Error('Failed to create company or desk');
      }

      // Step 3: Assign user to company with admin role
      await assignUser({
        variables: {
          email: approvalRequest.admin_email,
          companyId: company.id,
          deskId: desk.id,
          roleId: orgAdminRole.id
        }
      });

      return {
        success: true,
        company,
        desk,
        message: `Successfully created organization "${company.name}" and assigned admin access`
      };

    } catch (error) {
      console.error('Organization setup failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Organization setup failed'
      };
    }
  };

  return {
    completeApproval
  };
}

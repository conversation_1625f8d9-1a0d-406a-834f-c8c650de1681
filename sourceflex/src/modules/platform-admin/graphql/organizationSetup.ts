/**
 * Complete Organization Setup Mutations
 * Full workflow for creating companies from approved requests
 */

import { gql } from '@apollo/client';

// Get organization admin role ID
export const GET_ORG_ADMIN_ROLE = gql`
  query GetOrgAdminRole {
    roles(where: { name: { _eq: "org_admin" } }) {
      id
      name
      permissions
    }
  }
`;

// Complete organization setup workflow
export const COMPLETE_ORGANIZATION_SETUP = gql`
  mutation CompleteOrganizationSetup(
    $approvalId: uuid!
    $companyData: companies_insert_input!
    $deskData: desks_insert_input!
    $reviewNotes: String
  ) {
    # Update approval request to approved
    update_organization_approval_requests_by_pk(
      pk_columns: { id: $approvalId }
      _set: { 
        status: "approved"
        review_notes: $reviewNotes
        approved_at: "now()"
      }
    ) {
      id
      status
      admin_email
      admin_first_name
      admin_last_name
    }
    
    # Create company
    insert_companies_one(object: $companyData) {
      id
      name
      domain
      created_at
    }
    
    # Create default desk
    insert_desks_one(object: $deskData) {
      id
      name
      company_id
      type
    }
  }
`;

// Update user with company assignment
export const ASSIGN_USER_TO_COMPANY = gql`
  mutation AssignUserToCompany(
    $email: String!
    $companyId: uuid!
    $deskId: uuid!
    $roleId: uuid!
  ) {
    update_users(
      where: { email: { _eq: $email } }
      _set: { 
        company_id: $companyId
        desk_id: $deskId
        role_id: $roleId
      }
    ) {
      affected_rows
      returning {
        id
        email
        company_id
        desk_id
        role_id
      }
    }
  }
`;

// Get user by email for assignment
export const GET_USER_BY_EMAIL = gql`
  query GetUserByEmail($email: String!) {
    users(where: { email: { _eq: $email } }) {
      id
      email
      first_name
      last_name
      company_id
      role_id
    }
  }
`;

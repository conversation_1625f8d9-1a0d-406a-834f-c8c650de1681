/**
 * Platform Admin GraphQL Operations
 * Queries and mutations for organization approval workflow
 */

import { gql } from '@apollo/client';

// ============================================================================= 
// ORGANIZATION APPROVAL QUERIES
// =============================================================================

export const GET_PENDING_APPROVALS = gql`
  query GetPendingApprovals($limit: Int = 20, $offset: Int = 0) {
    organization_approval_requests(
      where: { status: { _eq: "pending" } }
      order_by: { created_at: desc }
      limit: $limit
      offset: $offset
    ) {
      id
      company_name
      domain
      admin_email
      admin_first_name
      admin_last_name
      status
      created_at
      company_size
      industry
      use_case
    }
    organization_approval_requests_aggregate(
      where: { status: { _eq: "pending" } }
    ) {
      aggregate {
        count
      }
    }
  }
`;

export const GET_APPROVAL_REQUEST_DETAILS = gql`
  query GetApprovalRequestDetails($id: uuid!) {
    organization_approval_requests_by_pk(id: $id) {
      id
      company_name
      domain
      admin_email
      admin_first_name
      admin_last_name
      status
      created_at
      updated_at
      company_size
      industry
      use_case
      review_notes
      reviewed_by
      approved_at
      duplicate_check_results
      is_existing_domain
    }
  }
`;

// =============================================================================
// ORGANIZATION APPROVAL MUTATIONS  
// =============================================================================

export const APPROVE_ORGANIZATION_REQUEST = gql`
  mutation ApproveOrganizationRequest($id: uuid!, $notes: String) {
    update_organization_approval_requests_by_pk(
      pk_columns: { id: $id }
      _set: { 
        status: "approved"
        review_notes: $notes
        approved_at: "now()"
      }
    ) {
      id
      status
      approved_at
    }
  }
`;

export const REJECT_ORGANIZATION_REQUEST = gql`
  mutation RejectOrganizationRequest($id: uuid!, $notes: String!) {
    update_organization_approval_requests_by_pk(
      pk_columns: { id: $id }
      _set: { 
        status: "rejected"
        review_notes: $notes
      }
    ) {
      id
      status
      review_notes
    }
  }
`;

// =============================================================================
// COMPANY CREATION MUTATIONS
// =============================================================================

export const CREATE_COMPANY_FROM_APPROVAL = gql`
  mutation CreateCompanyFromApproval(
    $companyData: companies_insert_input!
    $userData: user_profiles_insert_input!
  ) {
    insert_companies_one(object: $companyData) {
      id
      name
      domain
      created_at
    }
    insert_user_profiles_one(object: $userData) {
      id
      user_id
      company_id
      role_id
    }
  }
`;

export const GET_DEFAULT_SUBSCRIPTION_PLAN = gql`
  query GetDefaultSubscriptionPlan {
    subscription_plans(where: { name: { _eq: "starter" } }) {
      id
      name
      features
    }
  }
`;

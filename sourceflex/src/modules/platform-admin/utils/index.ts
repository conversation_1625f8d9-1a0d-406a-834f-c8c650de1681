export function canManageOrganizations(permissions: PlatformAdminPermissions): boolean {
  return hasPermission(permissions, 'manageOrganizations');
}

export function canManageSubscriptions(permissions: PlatformAdminPermissions): boolean {
  return hasPermission(permissions, 'manageSubscriptions');
}

export function canViewAnalytics(permissions: PlatformAdminPermissions): boolean {
  return hasPermission(permissions, 'viewAllAnalytics');
}

export function canManagePlatformAdmins(permissions: PlatformAdminPermissions): boolean {
  return hasPermission(permissions, 'managePlatformAdmins');
}

export function getPermissionLevel(permissions: PlatformAdminPermissions): 'basic' | 'advanced' | 'super' {
  const permissionCount = Object.values(permissions).filter(Boolean).length;
  
  if (permissionCount >= 5) return 'super';
  if (permissionCount >= 3) return 'advanced';
  return 'basic';
}

// =============================================================================
// VALIDATION UTILITIES
// =============================================================================

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain);
}

export function validateApprovalRequest(request: Partial<OrganizationApprovalRequest>): string[] {
  const errors: string[] = [];
  
  if (!request.companyName?.trim()) {
    errors.push('Company name is required');
  }
  
  if (!request.domain?.trim()) {
    errors.push('Domain is required');
  } else if (!isValidDomain(request.domain)) {
    errors.push('Invalid domain format');
  }
  
  if (!request.adminEmail?.trim()) {
    errors.push('Admin email is required');
  } else if (!isValidEmail(request.adminEmail)) {
    errors.push('Invalid email format');
  }
  
  if (!request.adminFirstName?.trim()) {
    errors.push('Admin first name is required');
  }
  
  if (!request.adminLastName?.trim()) {
    errors.push('Admin last name is required');
  }
  
  if (!request.requestedPlanId) {
    errors.push('Subscription plan is required');
  }
  
  return errors;
}

export function validatePlatformAdmin(admin: any): string[] {
  const errors: string[] = [];
  
  if (!admin.email?.trim()) {
    errors.push('Email is required');
  } else if (!isValidEmail(admin.email)) {
    errors.push('Invalid email format');
  }
  
  if (!admin.firstName?.trim()) {
    errors.push('First name is required');
  }
  
  if (!admin.lastName?.trim()) {
    errors.push('Last name is required');
  }
  
  if (!admin.role) {
    errors.push('Role is required');
  }
  
  return errors;
}

// =============================================================================
// METRICS AND ANALYTICS UTILITIES
// =============================================================================

export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

export function formatMetricValue(value: number, type: 'currency' | 'number' | 'percentage'): string {
  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(value);
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'number':
    default:
      return new Intl.NumberFormat('en-US').format(value);
  }
}

export function getMetricTrend(current: number, previous: number): 'up' | 'down' | 'stable' {
  const threshold = 0.01; // 1% threshold for "stable"
  const changePercent = Math.abs(calculateGrowthRate(current, previous));
  
  if (changePercent < threshold) return 'stable';
  return current > previous ? 'up' : 'down';
}

export function getTrendIcon(trend: 'up' | 'down' | 'stable'): string {
  const icons = {
    up: '📈',
    down: '📉',
    stable: '➡️'
  };
  return icons[trend];
}

export function getTrendColor(trend: 'up' | 'down' | 'stable'): string {
  const colors = {
    up: 'text-green-600',
    down: 'text-red-600',
    stable: 'text-gray-600'
  };
  return colors[trend];
}

// =============================================================================
// UTILIZATION CALCULATIONS
// =============================================================================

export function calculateUtilization(current: number, max?: number): number {
  if (!max || max === 0) return 0;
  return Math.min((current / max) * 100, 100);
}

export function getUtilizationLevel(percentage: number): 'low' | 'medium' | 'high' | 'critical' {
  if (percentage >= 90) return 'critical';
  if (percentage >= 75) return 'high';
  if (percentage >= 50) return 'medium';
  return 'low';
}

export function getUtilizationColor(percentage: number): string {
  const level = getUtilizationLevel(percentage);
  const colors = {
    low: 'text-green-600',
    medium: 'text-yellow-600',
    high: 'text-orange-600',
    critical: 'text-red-600'
  };
  return colors[level];
}

export function getUtilizationBarColor(percentage: number): string {
  const level = getUtilizationLevel(percentage);
  const colors = {
    low: 'bg-green-500',
    medium: 'bg-yellow-500',
    high: 'bg-orange-500',
    critical: 'bg-red-500'
  };
  return colors[level];
}

// =============================================================================
// FILTERING AND SORTING UTILITIES
// =============================================================================

export function filterApprovalsByStatus(
  approvals: OrganizationApprovalRequest[],
  statuses: ApprovalStatus[]
): OrganizationApprovalRequest[] {
  if (!statuses.length) return approvals;
  return approvals.filter(approval => statuses.includes(approval.status));
}

export function filterCompaniesByStatus(
  companies: Company[],
  statuses: SubscriptionStatus[]
): Company[] {
  if (!statuses.length) return companies;
  return companies.filter(company => statuses.includes(company.subscriptionStatus));
}

export function sortApprovalsByPriority(
  approvals: OrganizationApprovalRequest[]
): OrganizationApprovalRequest[] {
  return [...approvals].sort((a, b) => {
    const aPriority = a.status === 'pending' ? getPriorityLevel(a.hoursPending) : 'low';
    const bPriority = b.status === 'pending' ? getPriorityLevel(b.hoursPending) : 'low';
    
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    return priorityOrder[bPriority] - priorityOrder[aPriority];
  });
}

export function searchApprovals(
  approvals: OrganizationApprovalRequest[],
  searchTerm: string
): OrganizationApprovalRequest[] {
  if (!searchTerm.trim()) return approvals;
  
  const term = searchTerm.toLowerCase();
  return approvals.filter(approval =>
    approval.companyName.toLowerCase().includes(term) ||
    approval.domain.toLowerCase().includes(term) ||
    approval.adminEmail.toLowerCase().includes(term) ||
    approval.adminFirstName.toLowerCase().includes(term) ||
    approval.adminLastName.toLowerCase().includes(term)
  );
}

export function searchCompanies(
  companies: Company[],
  searchTerm: string
): Company[] {
  if (!searchTerm.trim()) return companies;
  
  const term = searchTerm.toLowerCase();
  return companies.filter(company =>
    company.name.toLowerCase().includes(term) ||
    company.domain?.toLowerCase().includes(term) ||
    company.industry?.toLowerCase().includes(term)
  );
}

// =============================================================================
// EXPORT AND IMPORT UTILITIES
// =============================================================================

export function exportToCSV(data: any[], filename: string): void {
  if (!data.length) return;
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' && value.includes(',') 
          ? `"${value}"` 
          : value;
      }).join(',')
    )
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.csv`;
  link.click();
  window.URL.revokeObjectURL(url);
}

export function exportApprovalsToCSV(approvals: OrganizationApprovalRequest[]): void {
  const exportData = approvals.map(approval => ({
    'Company Name': approval.companyName,
    'Domain': approval.domain,
    'Admin Email': approval.adminEmail,
    'Admin Name': `${approval.adminFirstName} ${approval.adminLastName}`,
    'Status': formatApprovalStatusText(approval.status),
    'Requested Plan': approval.requestedPlan?.name || 'N/A',
    'Company Size': approval.companySize || 'N/A',
    'Industry': approval.industry || 'N/A',
    'Hours Pending': approval.hoursPending ? formatPendingTime(approval.hoursPending) : 'N/A',
    'Created At': new Date(approval.createdAt).toLocaleDateString(),
    'Review Notes': approval.reviewNotes || 'N/A'
  }));
  
  exportToCSV(exportData, `organization-approvals-${new Date().toISOString().split('T')[0]}`);
}

export function exportCompaniesToCSV(companies: Company[]): void {
  const exportData = companies.map(company => ({
    'Company Name': company.name,
    'Domain': company.domain || 'N/A',
    'Subscription Status': formatSubscriptionStatusText(company.subscriptionStatus),
    'Plan': company.subscriptionPlan?.name || 'N/A',
    'Current Users': company.currentUsers,
    'Current Jobs': company.currentJobs,
    'Current Candidates': company.currentCandidates,
    'Max Users': company.subscriptionPlan?.maxUsers || 'Unlimited',
    'Max Jobs': company.subscriptionPlan?.maxJobs || 'Unlimited',
    'Max Candidates': company.subscriptionPlan?.maxCandidates || 'Unlimited',
    'Industry': company.industry || 'N/A',
    'Size Range': company.sizeRange || 'N/A',
    'Created At': new Date(company.createdAt).toLocaleDateString()
  }));
  
  exportToCSV(exportData, `companies-${new Date().toISOString().split('T')[0]}`);
}

// =============================================================================
// NOTIFICATION UTILITIES
// =============================================================================

export function generateApprovalNotificationMessage(
  approval: OrganizationApprovalRequest,
  action: 'approved' | 'rejected'
): string {
  const baseMessage = `Organization "${approval.companyName}" has been ${action}`;
  
  if (action === 'approved') {
    return `${baseMessage}. Welcome email will be sent to ${approval.adminEmail}.`;
  } else {
    return `${baseMessage}. Rejection notification will be sent to ${approval.adminEmail}.`;
  }
}

export function shouldShowUrgentAlert(metrics: PlatformMetrics): boolean {
  return (
    metrics.pendingApprovals > 10 ||
    metrics.averageApprovalTime > 48 ||
    metrics.activeSubscriptions === 0
  );
}

export function getUrgentAlertMessage(metrics: PlatformMetrics): string {
  const alerts = [];
  
  if (metrics.pendingApprovals > 10) {
    alerts.push(`${metrics.pendingApprovals} pending approvals need attention`);
  }
  
  if (metrics.averageApprovalTime > 48) {
    alerts.push(`Average approval time is ${metrics.averageApprovalTime.toFixed(1)} hours`);
  }
  
  if (metrics.activeSubscriptions === 0) {
    alerts.push('No active subscriptions found');
  }
  
  return alerts.join(', ');
}

// =============================================================================
// DATE RANGE UTILITIES
// =============================================================================

export function getDateRangePresets() {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  return {
    today: {
      start: today,
      end: now
    },
    yesterday: {
      start: new Date(today.getTime() - 24 * 60 * 60 * 1000),
      end: today
    },
    thisWeek: {
      start: new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000),
      end: now
    },
    lastWeek: {
      start: new Date(today.getTime() - (today.getDay() + 7) * 24 * 60 * 60 * 1000),
      end: new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000)
    },
    thisMonth: {
      start: new Date(now.getFullYear(), now.getMonth(), 1),
      end: now
    },
    lastMonth: {
      start: new Date(now.getFullYear(), now.getMonth() - 1, 1),
      end: new Date(now.getFullYear(), now.getMonth(), 0)
    },
    thisQuarter: {
      start: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1),
      end: now
    },
    thisYear: {
      start: new Date(now.getFullYear(), 0, 1),
      end: now
    }
  };
}

export function formatDateRange(start: Date, end: Date): string {
  const startStr = start.toLocaleDateString();
  const endStr = end.toLocaleDateString();
  
  if (startStr === endStr) {
    return startStr;
  }
  
  return `${startStr} - ${endStr}`;
}

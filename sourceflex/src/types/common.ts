// Global type definitions for SourceFlex

export type DeskType = 'recruitment' | 'bench_sales'

export type UserRole = 'admin' | 'manager' | 'recruiter' | 'bench_sales' | 'viewer'

export type SubscriptionTier = 'basic' | 'pro' | 'enterprise'

export type SubmissionStatus = 
  | 'submitted' 
  | 'client_reviewing' 
  | 'client_interested'
  | 'interview_scheduled' 
  | 'interviewed'
  | 'selected' 
  | 'rejected' 
  | 'withdrawn'

export type BenchStatus = 
  | 'available' 
  | 'submitted' 
  | 'interviewing' 
  | 'placed' 
  | 'ended'

export type JobStatus = 
  | 'draft' 
  | 'open' 
  | 'on_hold' 
  | 'filled' 
  | 'cancelled'

// Common interface for all entities
export interface BaseEntity {
  id: string
  created_at: string
  updated_at: string
}

// Organization context - every entity belongs to an organization
export interface OrganizationScoped {
  organization_id: string
}

// Desk context - entities scoped to specific desks
export interface DeskScoped {
  desk_id: string
}

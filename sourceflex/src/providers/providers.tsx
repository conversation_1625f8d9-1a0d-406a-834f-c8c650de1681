'use client'

import { NhostProvider } from '@nhost/nextjs'
import { NhostApolloProvider } from '@nhost/react-apollo'
import { nhost } from '@/lib/nhost'
import { useSessionManager } from '@/modules/auth'

interface ProvidersProps {
  children: React.ReactNode
}

function SessionProvider({ children }: ProvidersProps) {
  // Enterprise session configuration
  useSessionManager({
    persistentSession: false,      // Don't persist across browser restarts
    sessionTimeout: 480,          // 8 hours auto logout
    clearOnBrowserClose: true     // Clear session when browser closes
  });

  return <>{children}</>;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <NhostProvider nhost={nhost}>
      <NhostApolloProvider nhost={nhost}>
        <SessionProvider>
          {children}
        </SessionProvider>
      </NhostApolloProvider>
    </NhostProvider>
  )
}

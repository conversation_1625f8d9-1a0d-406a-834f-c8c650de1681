import Link from "next/link";

export function HomeHeader() {
  return (
    <header className="w-full max-w-6xl mx-auto">
      <nav className="flex items-center justify-between p-4">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">SF</span>
          </div>
          <span className="text-xl font-bold text-gray-900 dark:text-white">
            SourceFlex
          </span>
        </div>

        {/* Navigation Links */}
        <div className="hidden md:flex items-center space-x-6">
          <Link 
            href="/features" 
            className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
          >
            Features
          </Link>
          <Link 
            href="/pricing" 
            className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
          >
            Pricing
          </Link>
          <Link 
            href="/about" 
            className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
          >
            About
          </Link>
        </div>

        {/* Auth Buttons */}
        <div className="flex items-center space-x-3">
          <Link 
            href="/login"
            className="px-4 py-2 text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
          >
            Sign In
          </Link>
          <Link 
            href="/register"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Get Started
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="text-center pt-16 pb-8">
        <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
          Intelligent Talent Management
          <span className="block text-blue-600">for Modern Recruiters</span>
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
          AI-powered ATS with exclusive bench representation, desk-based workflows, 
          and comprehensive fraud prevention. Built for US recruitment firms.
        </p>
      </div>
    </header>
  );
}

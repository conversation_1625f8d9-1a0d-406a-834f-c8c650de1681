# Migration Guide: Current State to Full User Management System

**Current Session Progress & Next Steps**

---

## 🎯 Current Implementation Status

### ✅ **Completed in This Session**
- **OrgAdmin Module**: Complete 12-module compliant structure
- **Database Integration**: Real GraphQL queries (no mock data)
- **Permission System**: Role-based access control with `canManageUsers()`
- **Basic UI**: Simple org admin interface with real data display
- **Navigation**: OrgAdmin link in user dropdown (permission-gated)

### 📁 **Files Created/Modified**
```
src/modules/orgadmin/
├── components/
│   ├── SimpleOrgAdminLayout.tsx      ✅ Working with real data
│   ├── OrgAdminLayout.tsx            ⚠️  Needs ShadCN UI components
│   ├── UserManagement.tsx            ⚠️  Needs ShadCN UI components
│   └── [other components]            ⚠️  Needs ShadCN UI components
├── hooks/
│   ├── useOrgUsers.ts               ✅ Real database integration
│   ├── useOrgSettings.ts            ✅ Real database integration
│   └── [other hooks]               ✅ Ready for use
├── graphql/
│   └── queries.ts                   ✅ Real database queries
├── types/
│   └── index.ts                     ✅ Complete TypeScript interfaces
└── utils/                           ✅ Permission & validation utilities

app/(workspace)/orgadmin/
└── page.tsx                         ✅ Working page route

components/navigation/
└── DashboardNavigation.tsx          ✅ Added permission-gated link
```

---

## 🔧 **Immediate Migration Tasks (Next Session)**

### Priority 1: Fix UI Component Dependencies
**Issue**: Using ShadCN UI components that don't exist yet

**Solution Options**:
1. **Install ShadCN Components** (Recommended)
   ```bash
   npx shadcn-ui@latest add card button badge table tabs
   npx shadcn-ui@latest add input label select switch separator
   npx shadcn-ui@latest add avatar
   ```

2. **Use Simple Tailwind Layout** (Current working approach)
   - Continue with `SimpleOrgAdminLayout.tsx`
   - Add functionality before styling

### Priority 2: nHost Admin API Integration
**Current Gap**: OrgAdmin can only modify `user_profiles`, not `auth.users`

**Implementation Required**:
```
1. Setup nHost admin credentials in environment
2. Create Hasura Action for user deactivation
3. Integrate auth.users.disabled control
4. Test complete user deactivation flow
```

### Priority 3: Profile-Only Access Implementation
**Current Gap**: Pending users get "Access Denied" instead of profile interface

**Implementation Required**:
```
1. Modify ProtectedRoute.tsx for pending users
2. Create ProfileOnlyLayout component
3. Add pending approval status pages
4. Implement profile management for pending users
```

---

## 🗄️ **Database Verification Checklist**

Before proceeding, verify these tables exist and have correct structure:

### Required Tables Status
- ✅ `user_profiles` - Exists (confirmed via auth GraphQL queries)
- ✅ `companies` - Exists (confirmed via auth GraphQL queries)  
- ✅ `organization_approval_requests` - Exists (confirmed via auth GraphQL queries)
- ❓ `desks` - Need to verify structure
- ❓ `roles` - Need to verify structure
- ❓ `platform_admins` - Need to verify if exists

### Quick Database Verification
```sql
-- Run these to verify table structure
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_profiles', 'companies', 'desks', 'roles', 'platform_admins');
```

---

## 🚀 **Next Session Action Plan**

### Session Focus: Complete Foundation Setup
**Duration**: 2-3 hours

**Agenda**:
1. **Database Verification** (15 mins)
   - Verify all required tables exist
   - Check table structures match our GraphQL queries
   - Fix any schema mismatches

2. **UI Component Setup** (30 mins)
   - Install missing ShadCN components OR
   - Enhance SimpleOrgAdminLayout with more functionality

3. **nHost Admin API Integration** (60 mins)
   - Setup admin credentials securely
   - Create Hasura Action for user deactivation
   - Test auth.users.disabled functionality

4. **Profile-Only Access** (45 mins)
   - Modify ProtectedRoute for pending users
   - Create basic profile management interface
   - Test pending user experience

5. **Testing & Validation** (30 mins)
   - Test OrgAdmin access and permissions
   - Verify real data loading
   - Test user deactivation flow

### Key Deliverables
- ✅ Working OrgAdmin interface with real data
- ✅ Complete user deactivation (profile + auth)
- ✅ Profile-only access for pending users
- ✅ All database integrations verified

---

## 🔐 **Security Setup Required**

### Environment Variables to Add
```env
# nHost Admin API access (for user deactivation)
NHOST_ADMIN_SECRET=your_admin_secret_here

# Platform admin emails (for new org approvals)
PLATFORM_ADMIN_EMAILS=<EMAIL>

# Webhook secrets (for notifications)
NHOST_WEBHOOK_SECRET=your_webhook_secret_here
```

### Hasura Permissions to Verify
- OrgAdmin can query their company's user_profiles
- OrgAdmin can update user_profiles in their company
- Row-level security properly filters by company_id
- Platform admin access to organization_approval_requests

---

## 📋 **Testing Strategy**

### Manual Testing Checklist
- [ ] OrgAdmin user can access /orgadmin route
- [ ] Non-admin user cannot access /orgadmin route
- [ ] Real organization data loads correctly
- [ ] Real user profiles display correctly
- [ ] Real desk data shows correctly
- [ ] Navigation link appears only for authorized users

### Database Testing
- [ ] GraphQL queries return real data (not empty)
- [ ] User profiles connect to correct company
- [ ] Company data includes subscription info
- [ ] Desk aggregation counts work correctly

### Security Testing
- [ ] Permission checking works correctly
- [ ] Company data isolation enforced
- [ ] Admin actions require proper permissions
- [ ] Non-admin users properly blocked

---

## 🎯 **Success Criteria**

### This Foundation Phase Complete When:
1. ✅ OrgAdmin interface shows real database data
2. ✅ User deactivation works (both profile + auth)
3. ✅ Profile-only access implemented for pending users
4. ✅ All database integrations verified and working
5. ✅ Security permissions properly enforced

### Ready for Phase 2 When:
- User registration flow can create organization requests
- Profile-only users can manage their profiles
- Domain detection logic identifies existing companies
- Email notifications trigger properly

**Next Session**: Complete foundation setup and begin user registration enhancements.
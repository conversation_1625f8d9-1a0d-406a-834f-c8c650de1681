# User Registration & Organization Management System

**SourceFlex Enterprise-Grade User Onboarding & Administration**

---

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [User Journey Flows](#user-journey-flows)
3. [Implementation Phases](#implementation-phases)
4. [Database Schema](#database-schema)
5. [TODO Tracker](#todo-tracker)
6. [Migration Guide](#migration-guide)
7. [Security Considerations](#security-considerations)

---

## 🎯 System Overview

SourceFlex implements a **three-tier approval system** for enterprise-grade user management:

```
User Registration → Domain Detection → Organization Assignment → Admin Approval → Access Granted
```

### Key Principles
- **No Auto-Approval**: All new organizations require manual review
- **Domain-Based Detection**: Automatic existing company detection
- **Profile-Only Access**: Pending users can manage profiles while awaiting approval
- **Complete User Control**: OrgAdmins have full user lifecycle management

---

## 🚀 User Journey Flows

### Flow 1: New User with Existing Company Domain
```mermaid
graph TD
    A[User Registers] --> B[Email Domain Extracted]
    B --> C{Domain Exists?}
    C -->|Yes| D[Add to Existing Company]
    D --> E[Email to OrgAdmin]
    E --> F[OrgAdmin Reviews Request]
    F --> G{Approved?}
    G -->|Yes| H[User Gets Full Access]
    G -->|No| I[User Notified of Rejection]
    C -->|No| J[Flow 2: New Company]
```

**Timeline**: 1-24 hours (depends on OrgAdmin response)

### Flow 2: New User with New Company Domain
```mermaid
graph TD
    A[User Registers] --> B[No Existing Domain Found]
    B --> C[Create Organization Request]
    C --> D[Platform Admin Review Queue]
    D --> E[Platform Admin Reviews]
    E --> F{Company Approved?}
    F -->|Yes| G[Company Created + User as Admin]
    F -->|No| H[Request Rejected]
    G --> I[User Gets Full Access as OrgAdmin]
```

**Timeline**: 24-48 hours (Platform Admin manual review)

### Flow 3: Profile-Only Access State
```mermaid
graph TD
    A[User Email Verified] --> B[Pending Approval State]
    B --> C[Profile-Only Interface]
    C --> D[Can Update Profile]
    C --> E[View Approval Status]
    C --> F[Cannot Access Business Features]
    D --> G[Waits for Approval]
    E --> G
    F --> G
```

---

## 🏗️ Implementation Phases

### Phase 1: Foundation Setup ⚠️ **IN PROGRESS**
**Duration**: 2-3 sessions

**Core Infrastructure**
- ✅ OrgAdmin module structure
- ✅ Database schema validation
- ✅ Basic GraphQL queries
- ⏳ nHost Admin API integration
- ⏳ Profile-only access implementation
- ⏳ Domain detection logic

**Deliverables**:
- Working OrgAdmin interface
- Real database integration
- Permission-based access control

### Phase 2: User Registration Flow
**Duration**: 3-4 sessions

**Registration System**
- Enhanced registration form with company detection
- Email domain validation and company matching
- Organization approval request creation
- Profile-only access interface
- Email notification system

**Deliverables**:
- Complete user registration flow
- Profile management for pending users
- Domain detection and company association

### Phase 3: Platform Admin Workflow
**Duration**: 2-3 sessions

**Platform Administration**
- Platform admin dashboard
- Organization approval interface
- Company creation and configuration
- User management oversight
- Audit logging system

**Deliverables**:
- Platform admin tools
- Organization approval workflow
- System-wide user management

### Phase 4: OrgAdmin User Management
**Duration**: 3-4 sessions

**Organization Administration**
- User approval workflow for existing companies
- Complete user lifecycle management (invite/deactivate)
- Role and desk assignment
- User authentication control via nHost Admin API
- Bulk user operations

**Deliverables**:
- Complete OrgAdmin user management
- User deactivation (profile + auth)
- Role and permission management

### Phase 5: Notifications & Communication
**Duration**: 2-3 sessions

**Communication System**
- Email notifications for all approval states
- Real-time status updates
- Admin notification preferences
- User status change alerts
- System-wide announcements

**Deliverables**:
- Complete notification system
- Email templates and automation
- Real-time status updates

### Phase 6: Security & Compliance
**Duration**: 2-3 sessions

**Security Implementation**
- Complete audit logging
- Session management and security
- Data protection compliance
- Access control verification
- Security monitoring

**Deliverables**:
- SOC2/compliance ready system
- Complete audit trails
- Security monitoring dashboard

---

## 🗄️ Database Schema

### Core Tables

**users (nHost Auth)**
```sql
-- Built-in nHost authentication table
id              UUID PRIMARY KEY
email           VARCHAR(255) UNIQUE
disabled        BOOLEAN DEFAULT false  -- Platform/OrgAdmin control
email_verified  BOOLEAN DEFAULT false
created_at      TIMESTAMP
```

**user_profiles (Business Logic)**
```sql
-- Business profile data linking to auth.users
id              UUID PRIMARY KEY REFERENCES auth.users(id)
company_id      UUID REFERENCES companies(id)
desk_id         UUID REFERENCES desks(id) 
role_id         UUID REFERENCES roles(id)
is_active       BOOLEAN DEFAULT true     -- Business logic control
settings        JSONB DEFAULT '{}'
created_at      TIMESTAMP DEFAULT NOW()
updated_at      TIMESTAMP DEFAULT NOW()
```

**companies**
```sql
-- Organization/tenant management
id                        UUID PRIMARY KEY
name                      VARCHAR(255) NOT NULL
domain                    VARCHAR(255) UNIQUE
subscription_status       VARCHAR(20) DEFAULT 'trial'
subscription_plan_id      UUID REFERENCES subscription_plans(id)
approval_request_id       UUID REFERENCES organization_approval_requests(id)
approved_by              UUID REFERENCES platform_admins(id)
current_users            INTEGER DEFAULT 0
settings                 JSONB DEFAULT '{}'
```

**organization_approval_requests**
```sql
-- New organization approval workflow
id                    UUID PRIMARY KEY
company_name          VARCHAR(255) NOT NULL
domain               VARCHAR(255) NOT NULL
admin_email          VARCHAR(255) NOT NULL
admin_first_name     VARCHAR(100) NOT NULL  
admin_last_name      VARCHAR(100) NOT NULL
status               VARCHAR(20) DEFAULT 'pending'  -- pending, approved, rejected
reviewed_by          UUID REFERENCES platform_admins(id)
is_existing_domain   BOOLEAN DEFAULT false
created_at           TIMESTAMP DEFAULT NOW()
```

### Key Relationships
- `user_profiles.id` → `auth.users.id` (1:1)
- `user_profiles.company_id` → `companies.id` (N:1)
- `companies.approval_request_id` → `organization_approval_requests.id` (1:1)

---

## ✅ TODO Tracker

### 🔥 High Priority (Phase 1)
- [ ] **nHost Admin API Integration**
  - [ ] Setup admin credentials securely
  - [ ] Create Hasura Action for user deactivation
  - [ ] Test auth.users.disabled functionality
  - [ ] Error handling and logging

- [ ] **Profile-Only Access Implementation**
  - [ ] Modify ProtectedRoute for pending users
  - [ ] Create ProfileOnlyLayout component
  - [ ] Implement pending approval status pages
  - [ ] Add profile management interface

- [ ] **Domain Detection Logic**
  - [ ] Email domain extraction utility
  - [ ] Company domain matching queries
  - [ ] Public domain filtering
  - [ ] Domain suggestion system

### 🚧 Medium Priority (Phase 2)
- [ ] **Enhanced Registration Flow**
  - [ ] Company detection during registration
  - [ ] Organization approval request creation
  - [ ] Registration form enhancements
  - [ ] Email verification improvements

- [ ] **User Status Management**
  - [ ] Pending user tracking system
  - [ ] Status update mechanisms
  - [ ] User notification system
  - [ ] Admin notification triggers

### 📋 Lower Priority (Phase 3+)
- [ ] **Platform Admin Dashboard**
  - [ ] Organization approval interface
  - [ ] System-wide user management
  - [ ] Analytics and reporting
  - [ ] Audit log viewing

- [ ] **Advanced Features**
  - [ ] Bulk user operations
  - [ ] User import/export
  - [ ] Advanced permission management
  - [ ] Custom role creation

### 🔒 Security & Compliance
- [ ] **Audit Logging**
  - [ ] User action logging
  - [ ] Admin action tracking
  - [ ] Security event monitoring
  - [ ] Compliance reporting

- [ ] **Data Protection**
  - [ ] GDPR compliance features
  - [ ] Data retention policies
  - [ ] User data export
  - [ ] Right to deletion

---

## 🚀 Migration Guide

### Database Migrations Required

**Migration 1: Organization Approval System**
```sql
-- Already exists, verify structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'organization_approval_requests';
```

**Migration 2: Platform Admin System**
```sql
-- Verify platform_admins table exists
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'platform_admins';
```

**Migration 3: Enhanced User Profiles**
```sql
-- Add pending user tracking fields if needed
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS approval_status VARCHAR(20) DEFAULT 'approved';

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_approval_status 
ON user_profiles(approval_status);
```

### Code Migrations

**Step 1: Authentication Hook Updates**
- Update `useAuth` hook to handle pending states
- Add `approvalStatus` to AuthState interface
- Implement profile-only access detection

**Step 2: Route Protection Updates**
- Modify `ProtectedRoute` component
- Add profile-only route handling
- Implement pending user redirects

**Step 3: GraphQL Schema Sync**
```bash
# Generate new GraphQL types
npm run codegen

# Update GraphQL queries for new fields
# Verify Hasura permissions for new tables
```

### Environment Variables Required
```env
# nHost Admin API access
NHOST_ADMIN_SECRET=your_admin_secret
NHOST_WEBHOOK_SECRET=your_webhook_secret

# Email service configuration
RESEND_API_KEY=your_resend_key

# Platform admin configuration
PLATFORM_ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Hasura Configuration
1. **Add new tables to tracking**
2. **Configure row-level security policies**
3. **Set up admin action permissions**
4. **Create webhook endpoints for notifications**

---

## 🔐 Security Considerations

### Authentication Security
- **nHost Admin API**: Secure admin credential storage
- **Session Management**: Proper session invalidation on deactivation
- **Permission Boundaries**: OrgAdmins can only affect their company users

### Data Protection
- **Company Isolation**: Strict company_id filtering in all queries
- **Audit Trails**: Complete logging of all admin actions
- **Data Validation**: Input sanitization and validation at all levels

### Access Control
- **Role-Based Permissions**: Granular permission system
- **Profile-Only Access**: Limited functionality for pending users
- **Admin Oversight**: Platform admin approval for new organizations

---

## 📈 Success Metrics

### User Experience
- **Registration Completion Rate**: >95%
- **Approval Time**: <24 hours for existing companies, <48 hours for new
- **User Satisfaction**: Post-approval feedback >4.5/5

### Security & Compliance
- **Zero Unauthorized Access**: No pending users accessing business features
- **Complete Audit Trails**: 100% action logging
- **Admin Response Time**: <12 hours average

### System Performance
- **Page Load Times**: <2 seconds for all admin interfaces
- **Database Query Performance**: <500ms for user management operations
- **Real-time Updates**: <1 second notification delivery

---

**Last Updated**: Session 1 - Foundation Setup
**Next Session Focus**: nHost Admin API Integration + Profile-Only Access
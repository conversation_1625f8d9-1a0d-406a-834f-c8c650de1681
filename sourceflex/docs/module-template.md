# Module Documentation Template

Each module in SourceFlex should include comprehensive documentation. Use this template for creating module README files.

## Module Structure

```
modules/[module-name]/
├── README.md              # This documentation
├── components/            # UI components
│   ├── [entity]-list.tsx
│   ├── [entity]-form.tsx
│   ├── [entity]-card.tsx
│   └── [entity]-details.tsx
├── hooks/                 # React hooks
│   ├── use-[entity].ts
│   ├── use-[entity]-list.ts
│   └── use-[entity]-mutations.ts
├── types/                 # TypeScript types
│   └── index.ts
├── utils/                 # Utilities
│   └── index.ts
├── graphql/               # GraphQL operations
│   ├── queries.ts
│   └── mutations.ts
└── index.ts              # Module exports
```

## Module README Template

```markdown
# [Module Name] Module

Brief description of what this module does and its role in SourceFlex.

## Overview

### Purpose
Explain the main purpose and functionality of this module.

### Key Features
- Feature 1: Description
- Feature 2: Description
- Feature 3: Description

### Related Modules
- [Module A]: How it relates
- [Module B]: Integration points

## Components

### [Entity]List
Lists all entities with filtering, sorting, and pagination.

**Props:**
```typescript
interface EntityListProps {
  deskId: string;
  filters?: EntityFilters;
  onEdit?: (entity: Entity) => void;
  onDelete?: (entity: Entity) => void;
}
```

**Usage:**
```tsx
<EntityList
  deskId={user.desk_id}
  filters={currentFilters}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```

### [Entity]Form
Form component for creating and editing entities.

**Props:**
```typescript
interface EntityFormProps {
  entity?: Entity;
  onSubmit: (data: EntityInput) => Promise<void>;
  onCancel: () => void;
}
```

**Usage:**
```tsx
<EntityForm
  entity={editingEntity}
  onSubmit={handleSubmit}
  onCancel={() => setEditingEntity(null)}
/>
```

### [Entity]Card
Card display for individual entities in lists.

**Props:**
```typescript
interface EntityCardProps {
  entity: Entity;
  showActions?: boolean;
  onEdit?: (entity: Entity) => void;
  onView?: (entity: Entity) => void;
}
```

### [Entity]Details
Detailed view of a single entity.

**Props:**
```typescript
interface EntityDetailsProps {
  entityId: string;
  onEdit?: (entity: Entity) => void;
  onBack?: () => void;
}
```

## Hooks

### useEntity(id: string)
Fetches a single entity by ID.

**Returns:**
```typescript
{
  entity: Entity | null;
  loading: boolean;
  error: ApolloError | undefined;
  refetch: () => void;
}
```

### useEntityList(deskId: string, options?)
Fetches and manages a list of entities.

**Parameters:**
- `deskId`: The desk to filter entities
- `options`: Additional filtering and pagination options

**Returns:**
```typescript
{
  entities: Entity[];
  loading: boolean;
  error: ApolloError | undefined;
  pagination: PaginationInfo;
  refetch: () => void;
  loadMore: () => void;
}
```

### useEntityMutations()
Provides mutation functions for entity operations.

**Returns:**
```typescript
{
  createEntity: (input: EntityInput) => Promise<Entity>;
  updateEntity: (id: string, input: EntityInput) => Promise<Entity>;
  deleteEntity: (id: string) => Promise<void>;
  loading: boolean;
  error: ApolloError | undefined;
}
```

## Types

### Core Types
```typescript
interface Entity {
  id: string;
  company_id: string;
  desk_id: string;
  // ... other fields
  created_at: string;
  updated_at: string;
}

interface EntityInput {
  // Input fields for creating/updating
}

interface EntityFilters {
  // Filtering options
}
```

## GraphQL Operations

### Queries
- `GET_ENTITIES`: Fetch entities with filtering and pagination
- `GET_ENTITY`: Fetch single entity by ID
- `GET_ENTITY_SUMMARY`: Fetch entity summary data

### Mutations  
- `CREATE_ENTITY`: Create new entity
- `UPDATE_ENTITY`: Update existing entity
- `DELETE_ENTITY`: Delete entity

### Subscriptions
- `ENTITY_CHANGES`: Real-time updates for entities

## Business Logic

### Key Rules
1. Rule 1: Description and implementation
2. Rule 2: Description and implementation
3. Rule 3: Description and implementation

### Validation Rules
- Field validation requirements
- Business rule validations
- Cross-entity validations

### Multi-Tenant Considerations
- How data is scoped by company/desk
- Permission requirements
- Data isolation strategies

## Integration Points

### With Other Modules
- **[Module A]**: Integration description
- **[Module B]**: Shared data and workflows

### External Services
- **nHost Storage**: File uploads and management
- **Email Service**: Notifications and communications
- **AI Service**: Semantic search and matching

## Performance Considerations

### Query Optimization
- Use pagination for large datasets
- Implement proper indexing
- Use GraphQL fragments for reusable fields

### Component Optimization
- Memoize expensive components
- Use proper dependency arrays
- Implement virtual scrolling for large lists

### Caching Strategy
- Apollo Client cache configuration
- Cache invalidation patterns
- Background data refreshing

## Security

### Permission Requirements
- Who can view entities
- Who can create/edit entities
- Who can delete entities

### Data Validation
- Input sanitization
- Schema validation
- Business rule enforcement

### Multi-Tenant Security
- Company-level data isolation
- Desk-based access control
- Row Level Security policies

## Testing

### Unit Tests
Location of test files and testing strategy.

### Integration Tests
How module integrates with others.

### E2E Tests
End-to-end workflows to test.

## Future Enhancements

### Planned Features
- Feature 1: Timeline and requirements
- Feature 2: Timeline and requirements

### Technical Debt
- Known issues to address
- Performance improvements needed
- Code refactoring plans

## Migration Notes

### Breaking Changes
Document any breaking changes and migration paths.

### Database Changes
Required schema updates and data migrations.

### API Changes
GraphQL schema changes and client updates needed.
```

## Usage Instructions

1. **Copy this template** when creating a new module
2. **Fill in module-specific details** 
3. **Update as the module evolves**
4. **Keep examples current** with actual implementation
5. **Link to related documentation**

## Example Module Documentation

See existing modules for examples:
- `modules/auth/README.md` - Authentication module
- `modules/candidates/README.md` - Candidate management
- `modules/jobs/README.md` - Job posting and management

## Documentation Maintenance

- **Update after major changes** to the module
- **Keep code examples current** with implementation
- **Review during code reviews** 
- **Validate examples work** in practice
- **Link to relevant ADRs** for architectural decisions
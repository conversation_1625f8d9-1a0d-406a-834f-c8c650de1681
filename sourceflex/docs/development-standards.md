     });
     
     return {
       candidates: data?.candidates ?? [],
       loading,
       error,
       refetch: () => {
         // Refetch logic
       }
     };
   }
   
   // ✅ Use in components
   function CandidateList({ deskId }: { deskId: string }) {
     const { candidates, loading, error } = useCandidateList(deskId);
     
     if (loading) return <LoadingSpinner />;
     if (error) return <ErrorMessage error={error} />;
     
     return <div>{/* Render candidates */}</div>;
   }
   ```

### Form Handling Standards

1. **React Hook Form + Zod**
   ```typescript
   import { useForm } from 'react-hook-form';
   import { zodResolver } from '@hookform/resolvers/zod';
   import { z } from 'zod';
   
   const candidateSchema = z.object({
     firstName: z.string().min(1, 'First name is required'),
     lastName: z.string().min(1, 'Last name is required'),
     email: z.string().email('Invalid email format'),
     skills: z.array(z.string()).min(1, 'At least one skill required')
   });
   
   type CandidateFormData = z.infer<typeof candidateSchema>;
   
   export function CandidateForm() {
     const form = useForm<CandidateFormData>({
       resolver: zodResolver(candidateSchema),
       defaultValues: {
         firstName: '',
         lastName: '',
         email: '',
         skills: []
       }
     });
     
     return (
       <Form {...form}>
         {/* Form fields */}
       </Form>
     );
   }
   ```

### GraphQL Standards

1. **Query Organization**
   ```typescript
   // modules/candidates/graphql/queries.ts
   import { gql } from '@apollo/client';
   
   export const GET_CANDIDATES = gql`
     query GetCandidates($deskId: uuid!, $limit: Int, $offset: Int) {
       candidates(
         where: { desk_id: { _eq: $deskId } }
         limit: $limit
         offset: $offset
         order_by: { created_at: desc }
       ) {
         id
         bench_id
         first_name
         last_name
         current_title
         primary_skills
         availability_status
         created_at
       }
     }
   `;
   
   export const GET_CANDIDATE_DETAILS = gql`
     query GetCandidateDetails($id: uuid!) {
       candidate: candidates_by_pk(id: $id) {
         id
         bench_id
         first_name
         last_name
         email
         phone
         current_title
         current_company
         primary_skills
         secondary_skills
         # ... all fields needed for details view
       }
     }
   `;
   ```

2. **Mutation Standards**
   ```typescript
   // modules/candidates/graphql/mutations.ts
   export const CREATE_CANDIDATE = gql`
     mutation CreateCandidate($input: candidates_insert_input!) {
       insert_candidates_one(object: $input) {
         id
         bench_id
         first_name
         last_name
         created_at
       }
     }
   `;
   ```

## Multi-Tenant Architecture Rules

### 1. Always Scope by Company & Desk

```typescript
// ✅ Good - Always include company_id and desk_id
const { data } = useQuery(GET_CANDIDATES, {
  variables: {
    companyId: user.company_id,
    deskId: user.desk_id
  }
});

// ❌ Never query without tenant scoping
const { data } = useQuery(GET_ALL_CANDIDATES);
```

### 2. Subscription Feature Gating

```typescript
// utils/feature-gates.ts
export function hasFeature(
  subscription: SubscriptionPlan,
  feature: string
): boolean {
  return subscription.features[feature] === true;
}

// In components
function AdvancedSearchButton() {
  const { subscription } = useCompany();
  
  if (!hasFeature(subscription, 'advanced_search')) {
    return <UpgradePrompt feature="Advanced Search" />;
  }
  
  return <Button>Advanced Search</Button>;
}
```

### 3. Usage Limit Enforcement

```typescript
// Before creating new records
function useCreateCandidate() {
  const { subscription, currentCandidates } = useCompany();
  
  const createCandidate = async (data: CandidateInput) => {
    // Check limits
    if (subscription.max_candidates && 
        currentCandidates >= subscription.max_candidates) {
      throw new Error('Candidate limit reached');
    }
    
    // Proceed with creation
    return mutation({ variables: { input: data } });
  };
  
  return { createCandidate };
}
```

## Component Architecture

### 1. ShadCN UI Standards

```typescript
// Always extend base components
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SaveButtonProps extends React.ComponentProps<typeof Button> {
  isSaving?: boolean;
}

export function SaveButton({ 
  isSaving = false, 
  className, 
  children,
  ...props 
}: SaveButtonProps) {
  return (
    <Button
      className={cn(
        'min-w-[100px]',
        isSaving && 'opacity-50',
        className
      )}
      disabled={isSaving || props.disabled}
      {...props}
    >
      {isSaving ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Saving...
        </>
      ) : (
        children
      )}
    </Button>
  );
}
```

### 2. Dialog Management

```typescript
// Use controlled dialogs with state management
function CandidateListPage() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingCandidate, setEditingCandidate] = useState<Candidate | null>(null);
  
  return (
    <>
      <CandidateList 
        onEdit={setEditingCandidate}
        onCreateNew={() => setShowCreateDialog(true)}
      />
      
      <CreateCandidateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
      
      <EditCandidateDialog
        candidate={editingCandidate}
        open={!!editingCandidate}
        onOpenChange={(open) => !open && setEditingCandidate(null)}
      />
    </>
  );
}
```

## Error Handling Standards

### 1. GraphQL Error Handling

```typescript
function useCandidates(deskId: string) {
  const { data, loading, error } = useQuery(GET_CANDIDATES, {
    variables: { deskId },
    errorPolicy: 'all' // Show partial data if available
  });
  
  const handleError = (error: ApolloError) => {
    if (error.networkError) {
      toast.error('Network error. Please check your connection.');
    } else if (error.graphQLErrors.length > 0) {
      const message = error.graphQLErrors[0].message;
      toast.error(`Error: ${message}`);
    }
  };
  
  useEffect(() => {
    if (error) handleError(error);
  }, [error]);
  
  return {
    candidates: data?.candidates ?? [],
    loading,
    error,
    hasError: !!error
  };
}
```

### 2. Form Error Handling

```typescript
function CandidateForm() {
  const form = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema)
  });
  
  const [createCandidate] = useMutation(CREATE_CANDIDATE, {
    onError: (error) => {
      // Handle GraphQL validation errors
      if (error.message.includes('duplicate key')) {
        form.setError('email', {
          message: 'A candidate with this email already exists'
        });
      } else {
        toast.error('Failed to create candidate');
      }
    }
  });
  
  return <Form {...form}>{/* Form fields */}</Form>;
}
```

## Performance Standards

### 1. Query Optimization

```typescript
// ✅ Use fragments for reusable fields
const CANDIDATE_FRAGMENT = gql`
  fragment CandidateBasic on candidates {
    id
    bench_id
    first_name
    last_name
    current_title
    availability_status
  }
`;

// ✅ Pagination for large lists
const GET_CANDIDATES = gql`
  query GetCandidates($deskId: uuid!, $limit: Int = 20, $offset: Int = 0) {
    candidates(
      where: { desk_id: { _eq: $deskId } }
      limit: $limit
      offset: $offset
      order_by: { created_at: desc }
    ) {
      ...CandidateBasic
    }
    candidates_aggregate(where: { desk_id: { _eq: $deskId } }) {
      aggregate {
        count
      }
    }
  }
  ${CANDIDATE_FRAGMENT}
`;
```

### 2. Component Optimization

```typescript
// ✅ Memoize expensive components
const CandidateCard = memo(function CandidateCard({ 
  candidate 
}: { 
  candidate: Candidate 
}) {
  return (
    <Card>
      {/* Render candidate */}
    </Card>
  );
});

// ✅ Debounce search inputs
function SearchInput({ onSearch }: { onSearch: (query: string) => void }) {
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 300);
  
  useEffect(() => {
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch]);
  
  return (
    <Input
      value={query}
      onChange={(e) => setQuery(e.target.value)}
      placeholder="Search candidates..."
    />
  );
}
```

## Testing Standards

### 1. Component Testing

```typescript
// __tests__/candidate-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CandidateForm } from '../candidate-form';

describe('CandidateForm', () => {
  it('validates required fields', async () => {
    render(<CandidateForm />);
    
    fireEvent.click(screen.getByText('Save'));
    
    await waitFor(() => {
      expect(screen.getByText('First name is required')).toBeInTheDocument();
    });
  });
  
  it('submits valid data', async () => {
    const onSubmit = jest.fn();
    render(<CandidateForm onSubmit={onSubmit} />);
    
    fireEvent.change(screen.getByLabelText('First Name'), {
      target: { value: 'John' }
    });
    fireEvent.change(screen.getByLabelText('Last Name'), {
      target: { value: 'Doe' }
    });
    
    fireEvent.click(screen.getByText('Save'));
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        firstName: 'John',
        lastName: 'Doe'
      });
    });
  });
});
```

## Security Standards

### 1. Data Sanitization

```typescript
// Always sanitize user inputs
import { sanitize } from 'dompurify';
import { slugify } from 'slugify';

function createSlug(input: string): string {
  return slugify(input, {
    lower: true,
    strict: true,
    trim: true
  });
}

function sanitizeHtml(input: string): string {
  return sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
    ALLOWED_ATTR: []
  });
}
```

### 2. Permission Checks

```typescript
// Always check permissions before rendering
function DeleteCandidateButton({ candidate }: { candidate: Candidate }) {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  
  if (!hasPermission('candidates', 'delete', candidate)) {
    return null;
  }
  
  return (
    <Button
      variant="destructive"
      onClick={() => deleteCandidate(candidate.id)}
    >
      Delete
    </Button>
  );
}
```

## Documentation Standards

### 1. Component Documentation

```typescript
/**
 * CandidateCard displays candidate information in a card format
 * 
 * @param candidate - The candidate object to display
 * @param onEdit - Callback when edit button is clicked
 * @param showActions - Whether to show action buttons
 * @example
 * ```tsx
 * <CandidateCard
 *   candidate={candidate}
 *   onEdit={(candidate) => setEditingCandidate(candidate)}
 *   showActions={true}
 * />
 * ```
 */
interface CandidateCardProps {
  candidate: Candidate;
  onEdit?: (candidate: Candidate) => void;
  showActions?: boolean;
}

export function CandidateCard({ 
  candidate, 
  onEdit, 
  showActions = true 
}: CandidateCardProps) {
  // Implementation
}
```

### 2. Hook Documentation

```typescript
/**
 * Custom hook for managing candidate list state and operations
 * 
 * @param deskId - The desk ID to filter candidates
 * @param filters - Additional filters to apply
 * @returns Object containing candidates array, loading state, and operations
 * 
 * @example
 * ```tsx
 * const { candidates, loading, createCandidate } = useCandidates(deskId);
 * ```
 */
export function useCandidates(
  deskId: string,
  filters?: CandidateFilters
) {
  // Implementation
}
```

## File Naming Conventions

- **Components**: `kebab-case.tsx` (e.g., `candidate-form.tsx`)
- **Hooks**: `use-feature-name.ts` (e.g., `use-candidates.ts`)
- **Utilities**: `kebab-case.ts` (e.g., `date-utils.ts`)
- **Types**: `index.ts` or `feature-types.ts`
- **Constants**: `constants.ts` or `UPPER_SNAKE_CASE.ts`

## Git Commit Standards

```
type(scope): description

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes
- refactor: Code refactoring
- test: Test changes
- chore: Build/tooling changes

Examples:
feat(candidates): add bulk import functionality
fix(auth): resolve login redirect issue
docs(api): update GraphQL schema documentation
refactor(forms): extract reusable form components
```

## Code Review Checklist

- [ ] File size under 300 lines
- [ ] Proper TypeScript types
- [ ] Multi-tenant scoping implemented
- [ ] Error handling in place
- [ ] Performance considerations addressed
- [ ] Security checks implemented
- [ ] Tests written/updated
- [ ] Documentation updated
- [ ] ESLint rules passing
- [ ] No console.log statements
- [ ] Accessibility considerations
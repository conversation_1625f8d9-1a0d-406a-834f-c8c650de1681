# Architectural Decision Records (ADRs)

This document tracks key architectural decisions made during SourceFlex development.

## ADR-001: Multi-Tenant Architecture Design

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
SourceFlex needs to support multiple companies with complete data isolation while maintaining performance and scalability.

### Decision
Implement a multi-tenant architecture with:
- Company-level data isolation using `company_id` in all tables
- Desk-based role separation within companies
- Row Level Security (RLS) at the database level
- Subscription-based feature gating

### Consequences
- **Positive**: Complete data isolation, flexible pricing model, scalable architecture
- **Negative**: More complex queries, additional development overhead
- **Mitigation**: Helper functions for scoping, automated RLS policy generation

### Implementation
```sql
-- All main tables include company_id
CREATE TABLE candidates (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    desk_id UUID NOT NULL REFERENCES desks(id),
    -- other fields
);

-- RLS policies ensure isolation
CREATE POLICY "Company isolation" ON candidates 
FOR ALL USING (company_id = get_current_user_company_id());
```

---

## ADR-002: Unique Bench ID System

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Business Team, Technical Team  

### Context
Need a unique, human-readable identifier for candidates that prevents duplication and enables easy communication.

### Decision
Implement FFF+LLL+MMDD+SSSS format:
- FFF: First 3 letters of first name
- LLL: First 3 letters of last name  
- MMDD: Month and day of registration
- SSSS: Sequential number (4 digits)

### Consequences
- **Positive**: Unique identifiers, easy to communicate, prevents duplicates
- **Negative**: Potential for similar IDs with common names
- **Mitigation**: Sequential numbering ensures uniqueness

### Implementation
```sql
CREATE FUNCTION generate_bench_id(first_name_param VARCHAR, last_name_param VARCHAR) 
RETURNS VARCHAR(15) AS $$
-- Implementation generates JOHSMI121501234 format
$$;
```

---

## ADR-003: Technology Stack Selection

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Need a modern, scalable technology stack that supports rapid development and maintains high performance.

### Decision
**Frontend**:
- Next.js 14 with App Router for modern React patterns
- TypeScript for type safety
- ShadCN UI for consistent, accessible components
- Tailwind CSS for utility-first styling

**Backend**:
- nHost for managed backend (PostgreSQL + Auth + Storage)
- Hasura for auto-generated GraphQL API
- PostgreSQL with pgvector for semantic search

### Consequences
- **Positive**: Modern developer experience, type safety, rapid development
- **Negative**: Learning curve for team, vendor lock-in with nHost
- **Mitigation**: Abstraction layers for vendor services, comprehensive documentation

---

## ADR-004: Resume Fraud Prevention Strategy

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Business Team, Technical Team  

### Context
Resume fraud is a significant problem in recruitment, with candidates submitting modified or false documents.

### Decision
Implement multi-layered fraud prevention:
1. File hash comparison for exact duplicate detection
2. Content hash for text-based duplicate detection
3. Version tracking with change detection
4. OCR text extraction for content analysis

### Consequences
- **Positive**: Significantly reduces resume fraud, builds client trust
- **Negative**: Additional storage requirements, processing overhead
- **Mitigation**: Efficient hashing algorithms, background processing

### Implementation
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    file_hash VARCHAR(64) NOT NULL,     -- SHA-256 of file
    content_hash VARCHAR(64),           -- Hash of extracted text
    version INTEGER DEFAULT 1,         -- Version tracking
    extracted_text TEXT,               -- OCR/parsed content
    -- other fields
);

CREATE TABLE document_versions (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    version_number INTEGER,
    changes_detected JSONB,             -- What changed
    -- other fields
);
```

---

## ADR-005: Semantic Search Implementation

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Traditional keyword search is insufficient for matching candidates to jobs based on skills and requirements.

### Decision
Implement hybrid search combining:
1. Traditional full-text search with PostgreSQL's tsvector
2. Semantic search using pgvector with OpenAI embeddings
3. Weighted scoring combining both approaches

### Consequences
- **Positive**: Much better matching accuracy, improved user experience
- **Negative**: Additional complexity, external AI service dependency
- **Mitigation**: Fallback to traditional search, embedding caching

### Implementation
```sql
-- Traditional search vectors
search_vector tsvector,

-- Semantic search embeddings  
embedding vector(1536),

-- Indexes for both approaches
CREATE INDEX idx_candidates_search_vector ON candidates USING gin(search_vector);
CREATE INDEX idx_candidates_embedding ON candidates USING ivfflat(embedding);
```

---

## ADR-006: File Size and Modularity Strategy

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Large files become difficult to maintain and hit token limits in AI-assisted development.

### Decision
Enforce strict file size limits:
- **Hard limit**: 300 lines per file
- **Recommended split**: 200 lines
- **Proactive splitting**: Split files before reaching limits
- **Modular architecture**: 12 independent feature modules

### Consequences
- **Positive**: Better maintainability, easier code reviews, AI-friendly
- **Negative**: More files to manage, potential over-fragmentation
- **Mitigation**: Clear module structure, consistent patterns

### Implementation
```
src/modules/[module]/
├── components/     # UI components
├── hooks/         # React hooks  
├── types/         # TypeScript types
├── utils/         # Utilities
├── graphql/       # Queries/mutations
└── index.ts       # Module exports
```

---

## ADR-007: Subscription-Based Feature Gating

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Business Team, Technical Team  

### Context
Need a flexible pricing model that can grow with customer usage and feature needs.

### Decision
Implement three-tier subscription model:
- **Starter**: Basic features, usage limits
- **Professional**: Advanced features, higher limits  
- **Enterprise**: Full features, unlimited usage

Feature gating at multiple levels:
- Database constraints for usage limits
- UI component conditional rendering
- API endpoint access control

### Consequences
- **Positive**: Flexible monetization, clear upgrade path
- **Negative**: Complex permission system, feature flag management
- **Mitigation**: Centralized feature checking, automated limit enforcement

### Implementation
```typescript
// Feature checking utility
function hasFeature(subscription: SubscriptionPlan, feature: string): boolean {
  return subscription.features[feature] === true;
}

// Usage limit checking
function canCreateCandidate(company: Company): boolean {
  return !company.subscription.max_candidates || 
         company.current_candidates < company.subscription.max_candidates;
}
```

---

## ADR-008: GraphQL API Design

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Need an efficient, type-safe API that supports complex queries while maintaining security.

### Decision
Use Hasura auto-generated GraphQL with:
- Automatic CRUD operations from database schema
- Custom business logic in database functions
- Row Level Security for data isolation
- GraphQL Codegen for TypeScript type generation

### Consequences
- **Positive**: Rapid API development, type safety, real-time subscriptions
- **Negative**: Less control over API design, Hasura-specific patterns
- **Mitigation**: Custom functions for complex logic, abstraction layers

### Implementation
```typescript
// Auto-generated types
export type Candidate = {
  id: string;
  bench_id: string;
  first_name: string;
  last_name: string;
  // ... other fields
};

// Custom hooks for GraphQL operations
export function useCandidates(deskId: string) {
  return useQuery(GET_CANDIDATES, {
    variables: { deskId }
  });
}
```

---

## ADR-009: Authentication and Authorization

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Need secure, scalable authentication that integrates with multi-tenant architecture.

### Decision
Use nHost Auth with custom authorization:
- nHost handles authentication (login, signup, password reset)
- Custom role-based permissions in database
- JWT tokens with custom claims for company/desk context
- Row Level Security for data access control

### Consequences
- **Positive**: Secure authentication, flexible permissions, standards-based
- **Negative**: Complex permission management, custom authorization logic
- **Mitigation**: Helper functions, comprehensive testing

### Implementation
```sql
-- Custom roles and permissions
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(50),
    permissions JSONB NOT NULL
);

-- Users linked to companies and desks
CREATE TABLE users (
    id UUID PRIMARY KEY,        -- nHost auth user ID
    company_id UUID REFERENCES companies(id),
    desk_id UUID REFERENCES desks(id),
    role_id UUID REFERENCES roles(id)
);
```

---

## ADR-010: Error Handling Strategy

**Date**: 2024-12-05  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Need consistent error handling across the application that provides good user experience while maintaining security.

### Decision
Implement layered error handling:
1. **Database level**: Constraints and validation
2. **GraphQL level**: Hasura error responses
3. **Application level**: Custom error handling
4. **UI level**: User-friendly error messages

### Consequences
- **Positive**: Consistent error experience, better debugging
- **Negative**: Additional code complexity, multiple error sources
- **Mitigation**: Centralized error handling utilities, consistent patterns

### Implementation
```typescript
function useErrorHandler() {
  const handleError = (error: ApolloError) => {
    if (error.networkError) {
      toast.error('Network error. Please try again.');
    } else if (error.graphQLErrors.length > 0) {
      const message = error.graphQLErrors[0].message;
      toast.error(`Error: ${message}`);
    }
  };
  
  return { handleError };
}
```

---

## Decision Template

When making new architectural decisions, use this template:

```markdown
## ADR-XXX: [Decision Title]

**Date**: YYYY-MM-DD  
**Status**: [Proposed/Accepted/Rejected/Superseded]  
**Deciders**: [List of people involved]  

### Context
[Describe the situation and problem]

### Decision
[Describe the chosen solution]

### Consequences
- **Positive**: [Benefits of this decision]
- **Negative**: [Drawbacks or risks]
- **Mitigation**: [How to address the negatives]

### Implementation
[Code examples or architectural diagrams]
```

---

## Status Definitions

- **Proposed**: Decision is under consideration
- **Accepted**: Decision has been made and implemented
- **Rejected**: Decision was considered but not chosen
- **Superseded**: Decision has been replaced by a newer decision- **Negative**: Requires date of birth and SSN fields in candidate registration
- **Mitigation**: Clear validation, collision handling with 2-digit suffix

### Implementation
```sql
-- Updated function for correct Bench ID generation
CREATE OR REPLACE FUNCTION generate_bench_id(
    first_name_param VARCHAR(100),
    last_name_param VARCHAR(100),
    date_of_birth_param DATE,
    ssn_last_four_param VARCHAR(4)
) RETURNS VARCHAR(17) AS $$
-- Uses birth date MMDD and SSN last 4 digits with collision handling
$$;

-- Updated candidates table
ALTER TABLE candidates 
ADD COLUMN date_of_birth DATE,
ADD COLUMN ssn_last_four VARCHAR(4),
ALTER COLUMN bench_id TYPE VARCHAR(17);
```

---

## ADR-012: Platform Admin vs Organization Admin Separation

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Technical Team, Business Team  

### Context
Need clear distinction between SourceFlex internal team administrators and customer organization administrators to avoid confusion and ensure proper access control.

### Decision
**Implement Two-Tier Admin System:**

**Platform Level (SourceFlex Team):**
- Role: `platform_admin` 
- Module: `/modules/platform-admin/`
- Database: `platform_admins` table
- Permissions: Manage all organizations, subscriptions, platform settings

**Organization Level (Customer Companies):**
- Role: `org_admin`
- Module: `/modules/clients/` (consolidated)
- Database: `users` table with `org_admin` role
- Permissions: Manage only their company's data and settings

### Consequences
- **Positive**: Clear separation of concerns, better security model
- **Positive**: Platform admins can manage all organizations
- **Positive**: Organization admins limited to their company scope
- **Negative**: Additional complexity in permission management
- **Mitigation**: Clear documentation, role-based access control

---

## ADR-013: Manual Organization Approval Workflow

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Business Team, Technical Team  

### Context
All new organizations must be manually reviewed and approved by SourceFlex team to ensure quality and prevent abuse.

### Decision
**Implement Manual Approval Workflow:**
- All organization requests require platform admin approval
- No auto-approval regardless of criteria
- No time limits - approvals processed during business hours
- Domain detection for existing customers vs new organizations
- Email notifications for status changes
- Comprehensive approval tracking and audit trail

### Consequences
- **Positive**: Quality control, prevents spam/fake organizations
- **Positive**: Personal touch for new customers
- **Positive**: Better fraud prevention
- **Negative**: Potential delays in customer onboarding
- **Negative**: Requires platform admin availability
- **Mitigation**: Clear SLA communication, efficient approval dashboard

### Implementation
```sql
-- Organization approval workflow table
CREATE TABLE organization_approval_requests (
    id UUID PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    admin_email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, review
    reviewed_by UUID REFERENCES platform_admins(id),
    review_notes TEXT,
    is_existing_domain BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## ADR-014: Stripe Billing Integration Strategy

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Technical Team, Business Team  

### Context
Need comprehensive billing solution for SaaS subscription management with payment processing, invoicing, and subscription lifecycle management.

### Decision
**Use Stripe Billing Platform:**
- Stripe Billing for subscription management
- Stripe Checkout for payment collection
- Stripe Customer Portal for self-service
- Webhook integration for real-time updates
- Cost: 0.5% of revenue + 2.9% processing fees

**Key Features:**
- Subscription lifecycle management
- Failed payment recovery with AI
- Tax compliance and invoicing
- Multiple payment methods
- Usage-based billing support
- Revenue recognition automation

### Consequences
- **Positive**: Comprehensive billing platform, proven at scale
- **Positive**: Reduces development time significantly
- **Positive**: Built-in compliance and tax handling
- **Positive**: Customer self-service portal
- **Negative**: Additional 0.5% platform fee
- **Negative**: Vendor lock-in with Stripe
- **Mitigation**: Industry-standard choice, comprehensive features justify cost

### Implementation
```sql
-- Stripe integration tables
CREATE TABLE stripe_customers (
    company_id UUID REFERENCES companies(id),
    stripe_customer_id VARCHAR(255) UNIQUE,
    email VARCHAR(255) NOT NULL
);

CREATE TABLE stripe_subscriptions (
    stripe_subscription_id VARCHAR(255) UNIQUE,
    company_id UUID REFERENCES companies(id),
    status VARCHAR(50) NOT NULL,
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE
);

CREATE TABLE stripe_webhook_events (
    stripe_event_id VARCHAR(255) UNIQUE,
    event_type VARCHAR(100) NOT NULL,
    processed BOOLEAN DEFAULT false
);
```

---

## ADR-015: Consolidated 12-Module Architecture

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Original module structure was fragmented and exceeded optimal organization. Need to consolidate into 12 focused modules for better maintainability.

### Decision
**Final 12-Module Structure:**
```
1.  🔐 auth/           - Authentication & user management
2.  🏢 platform-admin/ - SourceFlex team administration  
3.  👥 candidates/     - Candidate management + bench tracking
4.  💼 jobs/          - Jobs + submissions + placements (consolidated)
5.  🔍 search/        - Semantic search + AI features (consolidated)
6.  📞 interviews/    - Interview scheduling & management
7.  🤝 clients/       - Client management + org admin (consolidated)
8.  📋 documents/     - Document management & fraud prevention
9.  💳 billing/       - Stripe integration + subscription management
10. 💬 communication/ - Messaging & notification system
11. 📊 analytics/     - Reports, dashboards & metrics
12. ⚙️ settings/      - System configuration & preferences
```

### Consequences
- **Positive**: Cleaner organization, focused modules
- **Positive**: Related functionality consolidated
- **Positive**: Easier navigation and maintenance
- **Negative**: Some modules larger than others
- **Mitigation**: Internal sub-organization within larger modules

---

## ADR-016: SSN Storage and Security Approach

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Technical Team, Business Team  

### Context
Need to store last 4 digits of SSN for Bench ID generation while maintaining security and compliance.

### Decision
**Plain Text Storage for Last 4 Digits:**
- Store only last 4 digits of SSN (not full SSN)
- Use plain text storage (no encryption required)
- Include validation for 4-digit format
- Clear data handling policies
- Regular security audits

**Rationale:**
- Last 4 digits alone are not sensitive PII
- Required for business-critical Bench ID system
- Most applications commonly display last 4 digits
- Encryption would add complexity without significant security benefit

### Consequences
- **Positive**: Simple implementation, no encryption overhead
- **Positive**: Meets business requirements for Bench ID
- **Positive**: Industry standard practice
- **Negative**: Still requires careful data handling
- **Mitigation**: Access logging, clear data policies, regular audits

### Implementation
```sql
-- Add SSN field to candidates table
ALTER TABLE candidates 
ADD COLUMN ssn_last_four VARCHAR(4) CHECK (ssn_last_four ~ '^\d{4}$');

-- Index for performance
CREATE INDEX idx_candidates_ssn_last_four ON candidates(ssn_last_four);
```

---

## ADR-017: Updated Search Vector and Embedding Strategy

**Date**: 2025-01-04  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
Need to update search functionality to work with corrected candidate fields and improved semantic search capabilities.

### Decision
**Hybrid Search Implementation:**
- Traditional full-text search using PostgreSQL tsvector
- Semantic search using pgvector with OpenAI embeddings
- Combined scoring for optimal results
- Support for both candidate and job matching
- Real-time search vector updates

### Consequences
- **Positive**: Best-in-class search capabilities
- **Positive**: Handles both keyword and semantic matching
- **Positive**: Scalable with growing data
- **Negative**: Requires OpenAI API for embeddings
- **Mitigation**: Embedding caching, fallback to traditional search

### Implementation
```sql
-- Updated search vector trigger
CREATE OR REPLACE FUNCTION update_candidate_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.first_name, '') || ' ' ||
        COALESCE(NEW.last_name, '') || ' ' ||
        COALESCE(NEW.current_title, '') || ' ' ||
        COALESCE(array_to_string(NEW.primary_skills, ' '), '') || ' ' ||
        COALESCE(NEW.current_location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

## Decision Implementation Checklist

**Database Schema Updates:**
- [x] Add platform_admins table
- [x] Add organization_approval_requests table  
- [x] Add Stripe integration tables
- [x] Update candidates table with date_of_birth and ssn_last_four
- [x] Update Bench ID generation function
- [x] Add proper indexes and RLS policies

**Module Structure Updates:**
- [x] Create platform-admin module
- [x] Create billing module  
- [ ] Reorganize existing modules per 12-module structure
- [ ] Update module exports and dependencies

**Authentication and Authorization:**
- [ ] Implement platform admin authentication
- [ ] Update RLS policies for new tables
- [ ] Create permission checking functions

**Stripe Integration:**
- [ ] Set up Stripe Billing configuration
- [ ] Implement webhook endpoints
- [ ] Create subscription management API
- [ ] Build billing dashboard components

**Documentation Updates:**
- [x] Update DECISIONS.md with corrections
- [ ] Update README.md with corrected architecture
- [ ] Create platform admin user guide
- [ ] Document Stripe integration setup

---

## Status Tracking

- **Database Schema**: ✅ Updated with all corrections
- **Platform Admin Module**: ✅ Core structure created  
- **Billing Module**: 🔄 In progress
- **Module Reorganization**: ⏳ Pending
- **Stripe Integration**: ⏳ Pending
- **Testing**: ⏳ Pending
- **Documentation**: 🔄 In progress

---

## Next Steps

1. **Complete Billing Module Implementation**
   - GraphQL operations
   - React hooks
   - Utility functions
   - Component structure

2. **Reorganize Existing Modules**
   - Consolidate jobs + submissions + placements
   - Merge search + AI features
   - Combine clients + org admin

3. **Implement Stripe Integration**
   - Webhook handlers
   - Subscription lifecycle management
   - Payment processing
   - Customer portal integration

4. **Update Documentation**
   - Architecture diagrams
   - API documentation
   - Deployment guides
   - User manuals

5. **Testing and Validation**
   - Unit tests for new functions
   - Integration tests for Stripe
   - End-to-end approval workflow testing
   - Performance testing with new schema

---

## ADR-008: Module Architecture Expansion

**Date**: 2025-01-07  
**Status**: Accepted  
**Deciders**: Technical Team  

### Context
During foundation setup implementation, we discovered the need for a dedicated Organization Administration module that was conceptually separate from the existing client management functionality.

### Decision
Expand from the original 12-module architecture to accommodate enterprise requirements:

**Updated Module Structure (13+ Modules)**:
1. **🔐 auth** - Authentication & user management
2. **🏢 platform-admin** - SourceFlex team administration  
3. **👥 candidates** - Candidate management + bench tracking
4. **💼 jobs** - Jobs + submissions + placements
5. **🔍 search** - Semantic search + AI features
6. **📞 interviews** - Interview scheduling & management
7. **🤝 clients** - Client management + org admin
8. **📋 documents** - Document management & fraud prevention
9. **💳 billing** - Stripe integration + subscription management
10. **💬 communication** - Messaging & notification system
11. **📊 analytics** - Reports, dashboards & metrics
12. **⚙️ settings** - System configuration & preferences
13. **🔧 orgadmin** - Organization administration & user management *(NEW)*

### Rationale
- **Clear Separation**: OrgAdmin functionality is distinct from client management
- **Enterprise Focus**: Dedicated module for user lifecycle management
- **Scalability**: Architecture can accommodate additional modules as needed
- **Clean Boundaries**: Better separation of concerns for complex enterprise features

### Implementation
- OrgAdmin module includes complete user deactivation (profile + auth)
- Real database integration with GraphQL queries
- ShadCN UI components for enhanced interface
- Permission-based access control

### Consequences
- **Positive**: Better organized codebase, clearer feature boundaries, enterprise-ready architecture
- **Negative**: Slightly increased complexity, module count no longer fixed
- **Mitigation**: Maintain strict module structure standards, clear documentation

### Future Considerations
- Architecture is now flexible to accommodate additional modules as business requirements evolve
- Each new module must follow the established structure: components/hooks/types/utils/graphql
- Module additions should be documented in subsequent ADRs

{"name": "sourceflex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch"}, "dependencies": {"@apollo/client": "^3.13.8", "@hookform/resolvers": "^5.1.1", "@nhost/nextjs": "^2.2.9", "@nhost/react": "^3.11.1", "@nhost/react-apollo": "^18.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "slugify": "^1.6.6", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.71"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/client-preset": "^4.8.3", "@graphql-codegen/introspection": "^4.0.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}
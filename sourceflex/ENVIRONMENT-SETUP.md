# 🚀 SourceFlex Environment Setup Guide

## 📦 **PACKAGES INSTALLED**

### ✅ Stripe Integration (COMPLETED)
```bash
npm install stripe @stripe/stripe-js @stripe/react-stripe-js --legacy-peer-deps
```

### ✅ nHost Integration (ALREADY INSTALLED)
```bash
@nhost/nextjs@^2.2.9
@nhost/react@^3.11.1  
@nhost/react-apollo@^18.0.1
```

### ✅ GraphQL & Development Tools (ALREADY INSTALLED)
```bash
@apollo/client@^3.13.8
@graphql-codegen/cli@^5.0.7
@graphql-codegen/client-preset@^4.8.3
```

---

## 🔧 **SETUP STEPS REQUIRED**

### **STEP 1: Stripe Account Setup**

1. **Create Stripe Account** (if not exists)
   - Go to [https://dashboard.stripe.com/register](https://dashboard.stripe.com/register)
   - Sign up with business email
   - Complete account verification

2. **Get API Keys**
   - Go to Developers → API Keys
   - **Test Mode** (toggle on top-right)
   - Copy **Publishable Key** (pk_test_...)
   - Copy **Secret Key** (sk_test_...)

3. **Update .env.local**
   ```bash
   STRIPE_SECRET_KEY=sk_test_your_actual_key_here
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_key_here
   ```

### **STEP 2: nHost Project Setup**

1. **Create nHost Account**
   - Go to [https://app.nhost.io](https://app.nhost.io)
   - Sign up with GitHub/email
   - Create new project

2. **Configure Project**
   - Project name: `sourceflex-backend`
   - Region: `us-east-1` (or closest to you)
   - Wait for provisioning (~2-3 minutes)

3. **Get Project Details**
   - Copy **Subdomain** (e.g., `sourceflex-abc123`)
   - Copy **Region** (e.g., `us-east-1`)

4. **Update .env.local**
   ```bash
   NEXT_PUBLIC_NHOST_SUBDOMAIN=your_actual_subdomain
   NEXT_PUBLIC_NHOST_REGION=your_actual_region
   ```

### **STEP 3: Database Schema Migration**

1. **Upload Schema to nHost**
   - Go to nHost Dashboard → Database → SQL Editor
   - Copy content from `/database/schema_updated.sql`
   - Run the schema (will take 2-3 minutes)

2. **Verify Tables Created**
   - Check Tables tab
   - Should see: companies, platform_admins, candidates, etc.

### **STEP 4: GraphQL Configuration**

1. **Update Codegen Config**
   - File: `codegen.ts`
   - Point to your nHost GraphQL endpoint
   - Generate types: `npm run codegen`

2. **Test GraphQL Connection**
   - Go to nHost Dashboard → GraphiQL
   - Run test query to verify connectivity

---

## 🎯 **IMMEDIATE NEXT STEPS**

### **Priority 1: Get Stripe Keys**
```bash
# Replace these in .env.local
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_KEY
```

### **Priority 2: Create nHost Project**
```bash
# Replace these in .env.local  
NEXT_PUBLIC_NHOST_SUBDOMAIN=YOUR_ACTUAL_SUBDOMAIN
NEXT_PUBLIC_NHOST_REGION=YOUR_ACTUAL_REGION
```

### **Priority 3: Run Database Migration**
- Upload `/database/schema_updated.sql` to nHost
- Verify all tables created successfully

### **Priority 4: Test Setup**
```bash
npm run dev
# Should start without errors
# GraphQL codegen should work
# Basic app should load
```

---

## 📋 **VERIFICATION CHECKLIST**

### ✅ **Stripe Setup**
- [ ] Stripe account created
- [ ] Test mode enabled
- [ ] API keys copied to .env.local
- [ ] Test webhook endpoint works

### ✅ **nHost Setup**  
- [ ] nHost project created
- [ ] Database schema uploaded
- [ ] GraphQL endpoint accessible
- [ ] Authentication configured

### ✅ **Development Environment**
- [ ] All packages installed without errors
- [ ] `npm run dev` starts successfully
- [ ] `npm run codegen` generates types
- [ ] Basic pages load without errors

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

1. **React Version Conflicts**
   ```bash
   npm install --legacy-peer-deps
   ```

2. **GraphQL Endpoint Not Found**
   - Verify nHost subdomain/region in .env.local
   - Check nHost project status in dashboard

3. **Stripe Keys Invalid**
   - Ensure test mode is enabled
   - Copy keys exactly (no extra spaces)
   - Restart dev server after updating .env.local

4. **Database Schema Errors**
   - Run schema in sections if large
   - Check for missing extensions
   - Verify admin privileges in nHost

---

## 💡 **RECOMMENDATIONS**

### **Development Workflow:**
1. Set up Stripe account first (fastest)
2. Create nHost project while testing Stripe
3. Upload database schema after nHost is ready
4. Test integrations incrementally

### **Production Considerations:**
- Enable Stripe live mode only after thorough testing
- Set up nHost production environment separately
- Configure proper webhook endpoints
- Set up monitoring and error tracking

---

## 🔗 **USEFUL LINKS**

- **Stripe Dashboard**: https://dashboard.stripe.com
- **nHost Dashboard**: https://app.nhost.io
- **Stripe Test Cards**: https://stripe.com/docs/testing
- **nHost Documentation**: https://docs.nhost.io
- **GraphQL Playground**: Access via nHost dashboard

---

**🎯 CURRENT STATUS**: 
- ✅ Packages installed
- ✅ Environment file prepared
- ⏳ Need Stripe API keys
- ⏳ Need nHost project setup
- ⏳ Need database migration

**📞 SUPPORT**: If you encounter issues, check the troubleshooting section or refer to official documentation.

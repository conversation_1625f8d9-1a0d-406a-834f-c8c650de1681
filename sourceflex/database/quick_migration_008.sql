-- Run this in Hasura Console SQL tab with admin secret: 8Rh10SmjSkaD2$^81^H%jK!Is'UwSOu7
-- Migration 008: Fix Users Table Conflict

ALTER TABLE users DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "User isolation" ON users;
ALTER TABLE users RENAME TO user_profiles;
ALTER TABLE user_profiles 
DROP COLUMN IF EXISTS email,
DROP COLUMN IF EXISTS first_name, 
DROP COLUMN IF EXISTS last_name,
DROP COLUMN IF EXISTS email_verified,
DROP COLUMN IF EXISTS last_login_at;

COMMENT ON TABLE user_profiles IS 'Business profile data for authenticated users - id references auth.users.id';

-- Update indexes
DROP INDEX IF EXISTS idx_users_company_id;
DROP INDEX IF EXISTS idx_users_desk_id;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_active;

CREATE INDEX idx_user_profiles_company_id ON user_profiles(company_id);
CREATE INDEX idx_user_profiles_desk_id ON user_profiles(desk_id);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);

-- Re-enable RLS with temporary policy
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User profile isolation" ON user_profiles FOR ALL USING (true);

-- Verification
SELECT 'Migration 008 completed successfully!' as result;
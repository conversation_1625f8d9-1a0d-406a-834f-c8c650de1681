-- PostgreSQL with pgvector extension for semantic search
-- Multi-tenant SaaS platform for recruitment and bench sales
-- UPDATED SCHEMA - Corrected Bench ID system and Platform Admin

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";

-- =============================================================================
-- CORE TENANT & SUBSCRIPTION MANAGEMENT
-- =============================================================================

-- Subscription plans and features
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE, -- starter, professional, enterprise
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2) NOT NULL,
    max_users INTEGER,
    max_jobs INTEGER,
    max_candidates INTEGER,
    features JSONB NOT NULL DEFAULT '{}', -- Feature flags
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Stripe integration fields
    stripe_product_id VARCHAR(255), -- Stripe product ID
    stripe_price_id_monthly VARCHAR(255), -- Stripe price ID for monthly
    stripe_price_id_yearly VARCHAR(255), -- Stripe price ID for yearly
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- PLATFORM ADMINISTRATION (SourceFlex Team)
-- =============================================================================

-- Platform admins table (SourceFlex team members)
CREATE TABLE platform_admins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'platform_admin', -- platform_admin, super_admin
    permissions JSONB NOT NULL DEFAULT '{}', -- Platform-level permissions
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization approval workflow
CREATE TABLE organization_approval_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    admin_email VARCHAR(255) NOT NULL,
    admin_first_name VARCHAR(100) NOT NULL,
    admin_last_name VARCHAR(100) NOT NULL,
    requested_plan_id UUID REFERENCES subscription_plans(id),
    
    -- Request details
    company_size VARCHAR(50),
    industry VARCHAR(100),
    use_case TEXT,
    
    -- Approval workflow
    status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, review
    reviewed_by UUID REFERENCES platform_admins(id),
    review_notes TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Detection flags
    is_existing_domain BOOLEAN DEFAULT false,
    duplicate_check_results JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company/Tenant management - UPDATED
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE, -- For SSO/domain verification
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    subscription_status VARCHAR(20) DEFAULT 'trial', -- trial, active, suspended, cancelled
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Stripe integration fields
    stripe_customer_id VARCHAR(255) UNIQUE, -- Stripe customer ID
    stripe_subscription_id VARCHAR(255), -- Current Stripe subscription ID
    
    -- Approval workflow connection
    approval_request_id UUID REFERENCES organization_approval_requests(id),
    approved_by UUID REFERENCES platform_admins(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Subscription limits tracking
    current_users INTEGER DEFAULT 0,
    current_jobs INTEGER DEFAULT 0,
    current_candidates INTEGER DEFAULT 0,
    
    -- Company settings
    settings JSONB DEFAULT '{}',
    logo_url TEXT,
    website VARCHAR(255),
    industry VARCHAR(100),
    size_range VARCHAR(50) -- 1-10, 11-50, 51-200, etc.
);

-- =============================================================================
-- DESK AND ROLE MANAGEMENT
-- =============================================================================

-- Desk types within companies (Recruitment vs BenchSales)
CREATE TABLE desks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL, -- "Recruitment", "Bench Sales", etc.
    type VARCHAR(20) NOT NULL CHECK (type IN ('recruitment', 'bench_sales')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(company_id, name)
);

-- Role definitions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_system_role BOOLEAN DEFAULT false, -- System roles cannot be deleted
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table - UPDATED with clear role separation
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID REFERENCES desks(id) ON DELETE SET NULL,
    role_id UUID NOT NULL REFERENCES roles(id),
    
    -- User status and settings
    is_active BOOLEAN NOT NULL DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- CANDIDATE MANAGEMENT - UPDATED WITH CORRECT BENCH ID
-- =============================================================================

-- Sequence for Bench ID generation
CREATE SEQUENCE IF NOT EXISTS bench_id_collision_sequence START 1;

-- Candidates table - UPDATED with SSN field and corrected Bench ID
CREATE TABLE candidates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    
    -- Unique bench ID: FFF+LLL+MMDD+SSSS+XX format
    bench_id VARCHAR(17) UNIQUE NOT NULL,
    
    -- Personal information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE, -- Required for MMDD in bench ID
    ssn_last_four VARCHAR(4), -- Last 4 digits of SSN (plain text, not encrypted)
    email VARCHAR(255),
    phone VARCHAR(20),
    alternate_email VARCHAR(255),
    alternate_phone VARCHAR(20),
    
    -- Location information
    current_location VARCHAR(255),
    preferred_locations TEXT[], -- Array of preferred work locations
    willing_to_relocate BOOLEAN DEFAULT false,
    
    -- Professional information
    current_title VARCHAR(255),
    current_company VARCHAR(255),
    total_experience_years DECIMAL(4,2),
    relevant_experience_years DECIMAL(4,2),
    
    -- Skills and expertise
    primary_skills TEXT[] NOT NULL, -- Core technical skills
    secondary_skills TEXT[], -- Additional skills
    certifications TEXT[], -- Professional certifications
    
    -- Availability and preferences
    availability_status VARCHAR(20) DEFAULT 'available', -- available, placed, interviewing, not_available
    availability_date DATE,
    notice_period_weeks INTEGER,
    
    -- Compensation expectations
    current_salary DECIMAL(12,2),
    expected_salary DECIMAL(12,2),
    salary_currency VARCHAR(3) DEFAULT 'USD',
    hourly_rate DECIMAL(8,2),
    
    -- Work preferences
    work_authorization VARCHAR(50), -- US Citizen, Green Card, H1B, etc.
    remote_preference VARCHAR(20) DEFAULT 'hybrid', -- remote, hybrid, onsite
    job_type_preference VARCHAR(20) DEFAULT 'permanent', -- permanent, contract, both
    
    -- Candidate source and tracking
    source VARCHAR(50), -- How candidate was acquired
    source_details TEXT,
    recruiter_id UUID REFERENCES users(id), -- Who recruited this candidate
    
    -- Search and matching
    search_vector tsvector, -- Full-text search
    embedding vector(1536), -- Semantic search embeddings
    
    -- Resume fraud prevention
    document_hashes TEXT[], -- Array of document file hashes
    content_hashes TEXT[], -- Array of document content hashes
    duplicate_flags JSONB DEFAULT '{}', -- Fraud detection results
    
    -- Audit trail
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- BENCH ID GENERATION FUNCTIONS - CORRECTED
-- =============================================================================

-- Updated function for correct Bench ID generation: FFF+LLL+MMDD+SSSS+XX
CREATE OR REPLACE FUNCTION generate_bench_id(
    first_name_param VARCHAR(100),
    last_name_param VARCHAR(100),
    date_of_birth_param DATE,
    ssn_last_four_param VARCHAR(4)
) RETURNS VARCHAR(17) AS $$
DECLARE
    first_part VARCHAR(3);
    last_part VARCHAR(3);
    birth_part VARCHAR(4);
    ssn_part VARCHAR(4);
    collision_suffix VARCHAR(2) := '00';
    final_bench_id VARCHAR(17);
    base_bench_id VARCHAR(15);
    collision_counter INTEGER := 0;
BEGIN
    -- Get first 3 characters of first name (uppercase, letters only)
    first_part := UPPER(SUBSTRING(REGEXP_REPLACE(first_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    first_part := RPAD(first_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get first 3 characters of last name (uppercase, letters only)
    last_part := UPPER(SUBSTRING(REGEXP_REPLACE(last_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    last_part := RPAD(last_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get month and day from date of birth (MMDD format)
    birth_part := TO_CHAR(date_of_birth_param, 'MMDD');
    
    -- Use last 4 digits of SSN
    ssn_part := LPAD(COALESCE(ssn_last_four_param, '0000'), 4, '0');
    
    -- Create base Bench ID (15 characters)
    base_bench_id := first_part || last_part || birth_part || ssn_part;
    
    -- Handle collisions with 2-digit suffix (00-99)
    LOOP
        final_bench_id := base_bench_id || collision_suffix;
        
        -- Check if this ID already exists
        IF NOT EXISTS (SELECT 1 FROM candidates WHERE bench_id = final_bench_id) THEN
            EXIT; -- Found unique ID
        END IF;
        
        -- Increment collision counter
        collision_counter := collision_counter + 1;
        collision_suffix := LPAD(collision_counter::text, 2, '0');
        
        -- Safety check to prevent infinite loop
        IF collision_counter >= 99 THEN
            RAISE EXCEPTION 'Unable to generate unique Bench ID after 99 attempts for %', base_bench_id;
        END IF;
    END LOOP;
    
    RETURN final_bench_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate bench ID for candidates - UPDATED
CREATE OR REPLACE FUNCTION trigger_generate_bench_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.bench_id IS NULL OR NEW.bench_id = '' THEN
        -- Require date_of_birth and ssn_last_four for Bench ID generation
        IF NEW.date_of_birth IS NULL THEN
            RAISE EXCEPTION 'Date of birth is required for Bench ID generation';
        END IF;
        
        NEW.bench_id := generate_bench_id(
            NEW.first_name,
            NEW.last_name,
            NEW.date_of_birth,
            NEW.ssn_last_four
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER candidates_generate_bench_id
    BEFORE INSERT ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION trigger_generate_bench_id();

-- Platform admin activity logs (separate from company logs)
CREATE TABLE platform_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID REFERENCES platform_admins(id),
    
    -- Action details
    action VARCHAR(100) NOT NULL, -- approve_org, reject_org, suspend_company, etc.
    entity_type VARCHAR(50) NOT NULL, -- organization_request, company, subscription, etc.
    entity_id UUID, -- ID of the affected entity
    
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    notes TEXT, -- Admin notes for the action
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Company and tenant indexes
CREATE INDEX idx_companies_domain ON companies(domain);
CREATE INDEX idx_companies_subscription_status ON companies(subscription_status);
CREATE INDEX idx_companies_stripe_customer_id ON companies(stripe_customer_id);

-- Platform admin indexes
CREATE INDEX idx_platform_admins_email ON platform_admins(email);
CREATE INDEX idx_platform_admins_active ON platform_admins(is_active);

-- Organization approval indexes
CREATE INDEX idx_org_approval_status ON organization_approval_requests(status);
CREATE INDEX idx_org_approval_domain ON organization_approval_requests(domain);
CREATE INDEX idx_org_approval_created ON organization_approval_requests(created_at);

-- User and role indexes
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_desk_id ON users(desk_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);

-- Candidate indexes
CREATE INDEX idx_candidates_company_id ON candidates(company_id);
CREATE INDEX idx_candidates_desk_id ON candidates(desk_id);
CREATE INDEX idx_candidates_bench_id ON candidates(bench_id);
CREATE INDEX idx_candidates_availability_status ON candidates(availability_status);
CREATE INDEX idx_candidates_search_vector ON candidates USING gin(search_vector);
CREATE INDEX idx_candidates_embedding ON candidates USING ivfflat(embedding vector_cosine_ops);
CREATE INDEX idx_candidates_ssn_last_four ON candidates(ssn_last_four);

-- Job indexes
CREATE INDEX idx_jobs_company_id ON jobs(company_id);
CREATE INDEX idx_jobs_desk_id ON jobs(desk_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_search_vector ON jobs USING gin(search_vector);
CREATE INDEX idx_jobs_embedding ON jobs USING ivfflat(embedding vector_cosine_ops);

-- Submission and placement indexes
CREATE INDEX idx_submissions_candidate_id ON submissions(candidate_id);
CREATE INDEX idx_submissions_job_id ON submissions(job_id);
CREATE INDEX idx_submissions_status ON submissions(status);
CREATE INDEX idx_placements_company_id ON placements(company_id);
CREATE INDEX idx_placements_status ON placements(status);

-- Stripe-related indexes
CREATE INDEX idx_stripe_customers_stripe_id ON stripe_customers(stripe_customer_id);
CREATE INDEX idx_stripe_subscriptions_stripe_id ON stripe_subscriptions(stripe_subscription_id);
CREATE INDEX idx_stripe_subscriptions_company ON stripe_subscriptions(company_id);
CREATE INDEX idx_stripe_invoices_stripe_id ON stripe_invoices(stripe_invoice_id);
CREATE INDEX idx_stripe_webhook_events_type ON stripe_webhook_events(event_type);
CREATE INDEX idx_stripe_webhook_events_processed ON stripe_webhook_events(processed);

-- Document indexes
CREATE INDEX idx_documents_candidate_id ON documents(candidate_id);
CREATE INDEX idx_documents_file_hash ON documents(file_hash);
CREATE INDEX idx_documents_content_hash ON documents(content_hash);

-- Activity log indexes
CREATE INDEX idx_activity_logs_company_id ON activity_logs(company_id);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_platform_activity_logs_admin_id ON platform_activity_logs(admin_id);
CREATE INDEX idx_platform_activity_logs_created_at ON platform_activity_logs(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tenant tables
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE desks ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Company isolation policies
CREATE POLICY "Company isolation" ON companies 
FOR ALL USING (id = get_current_user_company_id());

CREATE POLICY "Desk isolation" ON desks 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "User isolation" ON users 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Candidate isolation" ON candidates 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Job isolation" ON jobs 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Client isolation" ON clients 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Submission isolation" ON submissions 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Placement isolation" ON placements 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Document isolation" ON documents 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Message thread isolation" ON message_threads 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Message isolation" ON messages 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Metrics isolation" ON metrics 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Activity log isolation" ON activity_logs 
FOR ALL USING (company_id = get_current_user_company_id());

-- Platform admin tables - no RLS (full access for platform admins)
-- platform_admins, organization_approval_requests, platform_activity_logs

-- Stripe tables - company isolation
ALTER TABLE stripe_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Stripe customer isolation" ON stripe_customers 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Stripe subscription isolation" ON stripe_subscriptions 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Stripe invoice isolation" ON stripe_invoices 
FOR ALL USING (company_id = get_current_user_company_id());

-- =============================================================================
-- HELPER FUNCTIONS
-- =============================================================================

-- Function to get current user's company ID (to be implemented based on auth system)
CREATE OR REPLACE FUNCTION get_current_user_company_id()
RETURNS UUID AS $$
BEGIN
    -- This function will be implemented based on your auth system
    -- For now, returning NULL as placeholder
    RETURN NULL::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update search vectors for candidates
CREATE OR REPLACE FUNCTION update_candidate_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.first_name, '') || ' ' ||
        COALESCE(NEW.last_name, '') || ' ' ||
        COALESCE(NEW.current_title, '') || ' ' ||
        COALESCE(NEW.current_company, '') || ' ' ||
        COALESCE(array_to_string(NEW.primary_skills, ' '), '') || ' ' ||
        COALESCE(array_to_string(NEW.secondary_skills, ' '), '') || ' ' ||
        COALESCE(NEW.current_location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER candidates_search_vector_update
    BEFORE INSERT OR UPDATE ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION update_candidate_search_vector();

-- Function to update search vectors for jobs
CREATE OR REPLACE FUNCTION update_job_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.title, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.requirements, '') || ' ' ||
        COALESCE(array_to_string(NEW.required_skills, ' '), '') || ' ' ||
        COALESCE(array_to_string(NEW.preferred_skills, ' '), '') || ' ' ||
        COALESCE(NEW.location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER jobs_search_vector_update
    BEFORE INSERT OR UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_job_search_vector();

-- Function to log platform admin activities
CREATE OR REPLACE FUNCTION log_platform_admin_activity(
    admin_id_param UUID,
    action_param VARCHAR(100),
    entity_type_param VARCHAR(50),
    entity_id_param UUID DEFAULT NULL,
    old_values_param JSONB DEFAULT NULL,
    new_values_param JSONB DEFAULT NULL,
    notes_param TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO platform_activity_logs (
        admin_id,
        action,
        entity_type,
        entity_id,
        old_values,
        new_values,
        notes
    ) VALUES (
        admin_id_param,
        action_param,
        entity_type_param,
        entity_id_param,
        old_values_param,
        new_values_param,
        notes_param
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- DEFAULT DATA INSERTION
-- =============================================================================

-- Insert default subscription plans
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_users, max_jobs, max_candidates, features) VALUES
('starter', 99.00, 990.00, 5, 25, 100, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "email_support": true,
    "api_access": false,
    "advanced_analytics": false,
    "priority_support": false,
    "custom_integrations": false
}'),
('professional', 299.00, 2990.00, 25, 100, 500, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "advanced_analytics": true,
    "email_support": true,
    "phone_support": true,
    "api_access": true,
    "priority_support": false,
    "custom_integrations": false,
    "bulk_operations": true
}'),
('enterprise', 999.00, 9990.00, NULL, NULL, NULL, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "advanced_analytics": true,
    "custom_analytics": true,
    "email_support": true,
    "phone_support": true,
    "priority_support": true,
    "api_access": true,
    "custom_integrations": true,
    "bulk_operations": true,
    "white_label": true,
    "dedicated_account_manager": true
}');

-- Insert default roles
INSERT INTO roles (name, description, permissions, is_system_role) VALUES
('org_admin', 'Organization Administrator', '{
    "manage_users": true,
    "manage_desks": true,
    "manage_billing": true,
    "view_analytics": true,
    "manage_settings": true,
    "manage_integrations": true
}', true),
('recruiter', 'Recruiter', '{
    "manage_candidates": true,
    "manage_jobs": true,
    "manage_submissions": true,
    "view_analytics": false,
    "manage_clients": true
}', true),
('bench_sales', 'Bench Sales Representative', '{
    "view_candidates": true,
    "manage_placements": true,
    "manage_clients": true,
    "view_analytics": false
}', true),
('sales_manager', 'Sales Manager', '{
    "manage_candidates": true,
    "manage_jobs": true,
    "manage_submissions": true,
    "manage_placements": true,
    "manage_clients": true,
    "view_analytics": true,
    "manage_team": true
}', true),
('account_manager', 'Account Manager', '{
    "manage_clients": true,
    "view_candidates": true,
    "view_jobs": true,
    "view_submissions": true,
    "view_placements": true,
    "view_analytics": false
}', true);

-- Insert default platform admin (to be updated with actual admin details)
INSERT INTO platform_admins (email, first_name, last_name, role, permissions) VALUES
('<EMAIL>', 'System', 'Administrator', 'super_admin', '{
    "manage_organizations": true,
    "manage_subscriptions": true,
    "manage_platform_settings": true,
    "view_all_analytics": true,
    "manage_platform_admins": true,
    "system_maintenance": true
}');

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Organization approval dashboard view
CREATE VIEW organization_approval_dashboard AS
SELECT 
    oar.*,
    pa.first_name || ' ' || pa.last_name AS reviewed_by_name,
    sp.name AS requested_plan_name,
    sp.price_monthly,
    CASE 
        WHEN oar.status = 'pending' THEN EXTRACT(EPOCH FROM (NOW() - oar.created_at))/3600
        ELSE NULL 
    END AS hours_pending
FROM organization_approval_requests oar
LEFT JOIN platform_admins pa ON oar.reviewed_by = pa.id
LEFT JOIN subscription_plans sp ON oar.requested_plan_id = sp.id;

-- Company metrics summary view
CREATE VIEW company_metrics_summary AS
SELECT 
    c.id,
    c.name,
    c.subscription_status,
    sp.name AS plan_name,
    c.current_users,
    c.current_jobs,
    c.current_candidates,
    sp.max_users,
    sp.max_jobs,
    sp.max_candidates,
    CASE 
        WHEN sp.max_users IS NOT NULL THEN (c.current_users::FLOAT / sp.max_users * 100)
        ELSE NULL 
    END AS user_utilization_percentage,
    CASE 
        WHEN sp.max_candidates IS NOT NULL THEN (c.current_candidates::FLOAT / sp.max_candidates * 100)
        ELSE NULL 
    END AS candidate_utilization_percentage
FROM companies c
LEFT JOIN subscription_plans sp ON c.subscription_plan_id = sp.id;

-- =============================================================================
-- COMMENTS
-- =============================================================================

COMMENT ON TABLE platform_admins IS 'SourceFlex platform administrators (internal team)';
COMMENT ON TABLE organization_approval_requests IS 'Manual approval workflow for new organizations';
COMMENT ON TABLE companies IS 'Multi-tenant company/organization management with Stripe integration';
COMMENT ON TABLE stripe_customers IS 'Stripe customer data synchronized from Stripe';
COMMENT ON TABLE stripe_subscriptions IS 'Active Stripe subscriptions tracking';
COMMENT ON TABLE stripe_invoices IS 'Stripe invoice tracking and status';
COMMENT ON TABLE stripe_webhook_events IS 'Stripe webhook event processing log';
COMMENT ON TABLE candidates IS 'Candidate profiles with corrected Bench ID system (FFF+LLL+MMDD+SSSS+XX)';
COMMENT ON TABLE documents IS 'Document management with version control and fraud detection';
COMMENT ON TABLE jobs IS 'Job postings with semantic search capabilities';
COMMENT ON TABLE submissions IS 'Candidate job submissions tracking';
COMMENT ON TABLE placements IS 'Successful placements with guarantee tracking';
COMMENT ON TABLE activity_logs IS 'Comprehensive audit trail for company actions';
COMMENT ON TABLE platform_activity_logs IS 'Platform admin action audit trail';

-- Schema validation
DO $$
BEGIN
    ASSERT (SELECT COUNT(*) FROM subscription_plans) >= 3, 'Subscription plans not properly inserted';
    ASSERT (SELECT COUNT(*) FROM roles) >= 5, 'Default roles not properly inserted';
    ASSERT (SELECT COUNT(*) FROM platform_admins) >= 1, 'Platform admin not properly inserted';
    RAISE NOTICE 'SourceFlex CORRECTED database schema validation completed successfully!';
    RAISE NOTICE 'Key Updates: Corrected Bench ID system, Platform Admin module, Stripe integration, Organization approval workflow';
END $$;

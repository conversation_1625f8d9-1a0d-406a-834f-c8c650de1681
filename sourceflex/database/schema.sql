-- SourceFlex Database Schema
-- PostgreSQL with pgvector extension for semantic search
-- Multi-tenant SaaS platform for recruitment and bench sales

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";

-- =============================================================================
-- CORE TENANT & SUBSCRIPTION MANAGEMENT
-- =============================================================================

-- Subscription plans and features
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE, -- starter, professional, enterprise
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2) NOT NULL,
    max_users INTEGER,
    max_jobs INTEGER,
    max_candidates INTEGER,
    features JSONB NOT NULL DEFAULT '{}', -- Feature flags
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company/Tenant management
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE, -- For SSO/domain verification
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    subscription_status VARCHAR(20) DEFAULT 'trial', -- trial, active, suspended, cancelled
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Subscription limits tracking
    current_users INTEGER DEFAULT 0,
    current_jobs INTEGER DEFAULT 0,
    current_candidates INTEGER DEFAULT 0,
    
    -- Company settings
    settings JSONB DEFAULT '{}',
    logo_url TEXT,
    website VARCHAR(255),
    industry VARCHAR(100),
    size_range VARCHAR(50) -- 1-10, 11-50, 51-200, etc.
);

-- Desk management (Recruitment vs BenchSales separation)
CREATE TABLE desks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL, -- "Recruitment", "Bench Sales", etc.
    type VARCHAR(20) NOT NULL, -- recruitment, bench_sales
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(company_id, name)
);
-- =============================================================================
-- USER MANAGEMENT & AUTHENTICATION
-- =============================================================================

-- User roles and permissions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}', -- Detailed permissions object
    is_system_role BOOLEAN DEFAULT false, -- Admin, Super Admin system roles
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table (integrates with nHost auth)
CREATE TABLE users (
    id UUID PRIMARY KEY, -- Matches nHost auth user ID
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID REFERENCES desks(id),
    role_id UUID REFERENCES roles(id),
    
    -- Profile information
    avatar_url TEXT,
    phone VARCHAR(20),
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- User preferences and settings
    preferences JSONB DEFAULT '{}',
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- CLIENT MANAGEMENT
-- =============================================================================

CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    
    -- Client information
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- direct, staffing_agency, msp, etc.
    industry VARCHAR(100),
    website VARCHAR(255),
    description TEXT,
    
    -- Contact information
    primary_contact_name VARCHAR(255),
    primary_contact_email VARCHAR(255),
    primary_contact_phone VARCHAR(20),
    address JSONB, -- Structured address object
    
    -- Business details
    size_range VARCHAR(50),
    annual_revenue_range VARCHAR(50),
    
    -- Relationship management
    account_manager_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, prospect
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, critical
    
    -- Metadata
    tags TEXT[], -- Array of tags for categorization
    custom_fields JSONB DEFAULT '{}',
    created_by_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- =============================================================================
-- JOB MANAGEMENT
-- =============================================================================

CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id),
    
    -- Job details
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    nice_to_have TEXT,
    
    -- Location and work arrangement
    location VARCHAR(255),
    remote_type VARCHAR(20) DEFAULT 'hybrid', -- remote, hybrid, onsite
    travel_requirements VARCHAR(100),
    
    -- Financial details
    salary_min DECIMAL(12,2),
    salary_max DECIMAL(12,2),
    salary_currency VARCHAR(3) DEFAULT 'USD',
    hourly_rate_min DECIMAL(8,2),
    hourly_rate_max DECIMAL(8,2),
    fee_percentage DECIMAL(5,2), -- Commission percentage
    fee_amount DECIMAL(10,2), -- Fixed fee amount
    
    -- Job specifics
    job_type VARCHAR(20) NOT NULL, -- permanent, contract, contract_to_hire
    experience_level VARCHAR(20), -- entry, mid, senior, executive
    industry VARCHAR(100),
    department VARCHAR(100),
    
    -- Hiring details
    hiring_manager_name VARCHAR(255),
    hiring_manager_email VARCHAR(255),
    positions_available INTEGER DEFAULT 1,
    priority VARCHAR(10) DEFAULT 'medium',
    urgency VARCHAR(10) DEFAULT 'normal', -- normal, urgent, critical
    
    -- Status and workflow
    status VARCHAR(20) DEFAULT 'open', -- open, on_hold, filled, cancelled
    source VARCHAR(50), -- How job was acquired
    
    -- Assignment and ownership
    assigned_recruiter_id UUID REFERENCES users(id),
    created_by_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    tags TEXT[],
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    filled_at TIMESTAMP WITH TIME ZONE,
    
    -- Search vectors for semantic search
    search_vector tsvector,
    embedding vector(1536) -- OpenAI embedding dimensions
);

-- Index for full-text search
CREATE INDEX idx_jobs_search_vector ON jobs USING gin(search_vector);
CREATE INDEX idx_jobs_embedding ON jobs USING ivfflat(embedding) WITH (lists = 100);
CREATE INDEX idx_jobs_company_desk ON jobs(company_id, desk_id);
CREATE INDEX idx_jobs_status ON jobs(status) WHERE status IN ('open', 'on_hold');
-- =============================================================================
-- CANDIDATE & BENCH MANAGEMENT
-- =============================================================================

-- Generate unique bench ID sequence (FFF+LLL+MMDD+SSSS format)
CREATE SEQUENCE bench_id_sequence START 1;

CREATE TABLE candidates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    
    -- Unique bench ID for each candidate (FFF+LLL+MMDD+SSSS)
    bench_id VARCHAR(15) UNIQUE NOT NULL,
    
    -- Personal information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    alternate_email VARCHAR(255),
    alternate_phone VARCHAR(20),
    
    -- Location information
    current_location VARCHAR(255),
    preferred_locations TEXT[], -- Array of preferred work locations
    willing_to_relocate BOOLEAN DEFAULT false,
    
    -- Professional information
    current_title VARCHAR(255),
    current_company VARCHAR(255),
    total_experience_years DECIMAL(4,2),
    relevant_experience_years DECIMAL(4,2),
    
    -- Skills and expertise
    primary_skills TEXT[] NOT NULL, -- Core technical skills
    secondary_skills TEXT[], -- Additional skills
    certifications TEXT[], -- Professional certifications
    
    -- Availability and preferences
    availability_status VARCHAR(20) DEFAULT 'available', -- available, placed, interviewing, not_available
    availability_date DATE,
    notice_period_weeks INTEGER,
    
    -- Compensation expectations
    current_salary DECIMAL(12,2),
    expected_salary DECIMAL(12,2),
    salary_currency VARCHAR(3) DEFAULT 'USD',
    hourly_rate DECIMAL(8,2),
    
    -- Work preferences
    work_authorization VARCHAR(50), -- US Citizen, Green Card, H1B, etc.
    remote_preference VARCHAR(20) DEFAULT 'hybrid', -- remote, hybrid, onsite
    job_type_preference VARCHAR(20) DEFAULT 'permanent', -- permanent, contract, both
    
    -- Candidate source and tracking
    source VARCHAR(50), -- How candidate was acquired
    source_details TEXT,
    referrer_id UUID REFERENCES users(id), -- Who referred this candidate
    
    -- Status and management
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, blacklisted, placed
    priority VARCHAR(10) DEFAULT 'medium',
    last_contacted_at TIMESTAMP WITH TIME ZONE,
    
    -- Assignment
    owner_id UUID NOT NULL REFERENCES users(id), -- Primary recruiter/sales person
    created_by_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    tags TEXT[],
    notes TEXT,
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Search vectors for semantic search
    search_vector tsvector,
    embedding vector(1536)
);

-- Indexes for candidates
CREATE INDEX idx_candidates_bench_id ON candidates(bench_id);
CREATE INDEX idx_candidates_search_vector ON candidates USING gin(search_vector);
CREATE INDEX idx_candidates_embedding ON candidates USING ivfflat(embedding) WITH (lists = 100);
CREATE INDEX idx_candidates_company_desk ON candidates(company_id, desk_id);
CREATE INDEX idx_candidates_status ON candidates(status);
CREATE INDEX idx_candidates_skills ON candidates USING gin(primary_skills);
CREATE INDEX idx_candidates_availability ON candidates(availability_status, availability_date);
-- =============================================================================
-- DOCUMENT MANAGEMENT & RESUME FRAUD PREVENTION
-- =============================================================================

CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
    
    -- Document details
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL, -- nHost storage path
    
    -- Document type and categorization
    document_type VARCHAR(50) NOT NULL, -- resume, cover_letter, portfolio, etc.
    is_primary BOOLEAN DEFAULT false, -- Primary resume flag
    
    -- Fraud prevention
    version INTEGER DEFAULT 1, -- Resume version tracking
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for duplicate detection
    content_hash VARCHAR(64), -- Content-based hash (extracted text)
    extracted_text TEXT, -- OCR/parsed text content
    
    -- Metadata and tracking
    uploaded_by_id UUID NOT NULL REFERENCES users(id),
    upload_source VARCHAR(50), -- manual, email, api, etc.
    tags TEXT[],
    notes TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- active, archived, flagged
    is_verified BOOLEAN DEFAULT false,
    verification_notes TEXT,
    verified_by_id UUID REFERENCES users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Search vector for document content
    search_vector tsvector,
    embedding vector(1536)
);

-- Document version history for fraud prevention
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    content_hash VARCHAR(64),
    changes_detected JSONB, -- What changed compared to previous version
    uploaded_by_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(document_id, version_number)
);

-- Indexes for documents
CREATE INDEX idx_documents_candidate ON documents(candidate_id);
CREATE INDEX idx_documents_type ON documents(document_type);
CREATE INDEX idx_documents_hash ON documents(file_hash);
CREATE INDEX idx_documents_content_hash ON documents(content_hash);
CREATE INDEX idx_documents_search_vector ON documents USING gin(search_vector);
CREATE INDEX idx_documents_embedding ON documents USING ivfflat(embedding) WITH (lists = 100);
-- =============================================================================
-- SUBMISSIONS & PLACEMENTS
-- =============================================================================

CREATE TABLE submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id),
    candidate_id UUID NOT NULL REFERENCES candidates(id),
    
    -- Submission details
    submitted_rate DECIMAL(12,2), -- Salary/rate submitted
    submitted_rate_type VARCHAR(20), -- annual, hourly
    bill_rate DECIMAL(12,2), -- For contract positions
    
    -- Status tracking
    status VARCHAR(30) DEFAULT 'submitted', -- submitted, screening, interview, rejected, offer, placed
    status_notes TEXT,
    
    -- Important dates
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_interview_at TIMESTAMP WITH TIME ZONE,
    offer_date DATE,
    start_date DATE,
    
    -- Assignment
    submitted_by_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    notes TEXT,
    tags TEXT[],
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(job_id, candidate_id) -- Prevent duplicate submissions
);

CREATE TABLE placements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    submission_id UUID NOT NULL REFERENCES submissions(id),
    
    -- Placement financial details
    final_salary DECIMAL(12,2) NOT NULL,
    bill_rate DECIMAL(12,2), -- For contract placements
    fee_earned DECIMAL(12,2) NOT NULL,
    fee_percentage DECIMAL(5,2),
    
    -- Placement dates
    start_date DATE NOT NULL,
    end_date DATE, -- For contract placements
    
    -- Placement type
    placement_type VARCHAR(20) NOT NULL, -- permanent, contract, contract_to_hire
    contract_duration_months INTEGER, -- For contract positions
    
    -- Guarantee and follow-up
    guarantee_period_days INTEGER DEFAULT 90,
    replacement_guarantee BOOLEAN DEFAULT true,
    follow_up_30_days TIMESTAMP WITH TIME ZONE,
    follow_up_60_days TIMESTAMP WITH TIME ZONE,
    follow_up_90_days TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- active, completed, replaced, terminated
    termination_reason TEXT,
    termination_date DATE,
    
    -- Assignment
    placed_by_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    notes TEXT,
    celebration_sent BOOLEAN DEFAULT false, -- Team celebration notification
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for submissions and placements
CREATE INDEX idx_submissions_job_candidate ON submissions(job_id, candidate_id);
CREATE INDEX idx_submissions_status ON submissions(status);
CREATE INDEX idx_submissions_desk ON submissions(desk_id);
CREATE INDEX idx_placements_submission ON placements(submission_id);
CREATE INDEX idx_placements_status ON placements(status);
CREATE INDEX idx_placements_dates ON placements(start_date, end_date);
-- =============================================================================
-- INTERVIEW MANAGEMENT
-- =============================================================================

CREATE TABLE interviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    submission_id UUID NOT NULL REFERENCES submissions(id),
    
    -- Interview details
    title VARCHAR(255) NOT NULL,
    type VARCHAR(30) NOT NULL, -- phone, video, onsite, technical, final
    round_number INTEGER DEFAULT 1,
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    timezone VARCHAR(50),
    
    -- Location/Platform
    location TEXT, -- Office address or meeting room
    meeting_link TEXT, -- Zoom, Teams, etc.
    dial_in_details TEXT,
    
    -- Participants
    interviewer_name VARCHAR(255),
    interviewer_email VARCHAR(255),
    interviewer_phone VARCHAR(20),
    panel_members TEXT[], -- For panel interviews
    
    -- Status and outcomes
    status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, completed, cancelled, no_show
    outcome VARCHAR(20), -- positive, negative, neutral, pending
    feedback TEXT,
    next_steps TEXT,
    
    -- Ratings (1-5 scale)
    technical_rating INTEGER CHECK (technical_rating >= 1 AND technical_rating <= 5),
    communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
    cultural_fit_rating INTEGER CHECK (cultural_fit_rating >= 1 AND cultural_fit_rating <= 5),
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
    
    -- Tracking
    reminder_sent BOOLEAN DEFAULT false,
    feedback_received BOOLEAN DEFAULT false,
    scheduled_by_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- COMMUNICATION & ACTIVITY TRACKING
-- =============================================================================

CREATE TABLE communications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Communication type and direction
    type VARCHAR(30) NOT NULL, -- email, call, sms, meeting, note
    direction VARCHAR(10) NOT NULL, -- inbound, outbound
    
    -- Related entities (at least one must be specified)
    candidate_id UUID REFERENCES candidates(id),
    client_id UUID REFERENCES clients(id),
    job_id UUID REFERENCES jobs(id),
    submission_id UUID REFERENCES submissions(id),
    
    -- Communication content
    subject VARCHAR(500),
    content TEXT,
    
    -- Communication details
    from_email VARCHAR(255),
    to_emails TEXT[], -- Array of recipient emails
    cc_emails TEXT[],
    bcc_emails TEXT[],
    
    -- Call details
    phone_number VARCHAR(20),
    call_duration_minutes INTEGER,
    
    -- Status and tracking
    status VARCHAR(20) DEFAULT 'completed', -- scheduled, completed, failed
    is_important BOOLEAN DEFAULT false,
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    
    -- Assignment
    user_id UUID NOT NULL REFERENCES users(id),
    
    -- Metadata
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for interviews and communications
CREATE INDEX idx_interviews_submission ON interviews(submission_id);
CREATE INDEX idx_interviews_scheduled_at ON interviews(scheduled_at);
CREATE INDEX idx_interviews_status ON interviews(status);
CREATE INDEX idx_communications_candidate ON communications(candidate_id);
CREATE INDEX idx_communications_client ON communications(client_id);
CREATE INDEX idx_communications_job ON communications(job_id);
CREATE INDEX idx_communications_type_direction ON communications(type, direction);
CREATE INDEX idx_communications_follow_up ON communications(follow_up_required, follow_up_date);
-- =============================================================================
-- NOTIFICATIONS & ALERTS
-- =============================================================================

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Notification details
    type VARCHAR(50) NOT NULL, -- interview_reminder, follow_up_due, placement_anniversary, etc.
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Recipients
    user_id UUID REFERENCES users(id), -- Individual notification
    desk_id UUID REFERENCES desks(id), -- Desk-wide notification
    is_company_wide BOOLEAN DEFAULT false, -- Company-wide notification
    
    -- Related entities
    related_entity_type VARCHAR(50), -- candidate, job, client, submission, etc.
    related_entity_id UUID,
    
    -- Status and delivery
    status VARCHAR(20) DEFAULT 'pending', -- pending, sent, read, dismissed
    delivery_method VARCHAR(20) DEFAULT 'in_app', -- in_app, email, sms
    
    -- Scheduling
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Priority and categorization
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent
    category VARCHAR(30), -- reminder, alert, announcement, celebration
    
    -- Metadata
    action_url TEXT, -- Deep link to relevant page
    action_text VARCHAR(100), -- Button text for action
    created_by_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- SEARCH & ANALYTICS
-- =============================================================================

-- Saved searches for quick access
CREATE TABLE saved_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    
    -- Search details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    search_type VARCHAR(30) NOT NULL, -- candidates, jobs, clients
    
    -- Search criteria (stored as JSON)
    criteria JSONB NOT NULL,
    
    -- Settings
    is_alert BOOLEAN DEFAULT false, -- Email when new matches found
    alert_frequency VARCHAR(20), -- daily, weekly, monthly
    last_alert_sent TIMESTAMP WITH TIME ZONE,
    
    -- Usage tracking
    use_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search analytics for improving search experience
CREATE TABLE search_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    
    -- Search details
    search_type VARCHAR(30) NOT NULL,
    query_text TEXT,
    filters_applied JSONB,
    
    -- Results and interaction
    results_count INTEGER NOT NULL,
    clicked_result_ids UUID[],
    result_position_clicked INTEGER,
    
    -- Timing
    search_duration_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- =============================================================================
-- TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Function to generate unique bench IDs (FFF+LLL+MMDD+SSSS format)
CREATE OR REPLACE FUNCTION generate_bench_id(
    first_name_param VARCHAR(100),
    last_name_param VARCHAR(100)
) RETURNS VARCHAR(15) AS $$
DECLARE
    first_part VARCHAR(3);
    last_part VARCHAR(3);
    date_part VARCHAR(4);
    sequence_part VARCHAR(4);
    sequence_num INTEGER;
    final_bench_id VARCHAR(15);
BEGIN
    -- Get first 3 characters of first name (uppercase)
    first_part := UPPER(SUBSTRING(REGEXP_REPLACE(first_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    first_part := RPAD(first_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get first 3 characters of last name (uppercase)
    last_part := UPPER(SUBSTRING(REGEXP_REPLACE(last_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    last_part := RPAD(last_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get current month and day (MMDD format)
    date_part := TO_CHAR(CURRENT_DATE, 'MMDD');
    
    -- Get next sequence number and format as 4-digit string
    sequence_num := nextval('bench_id_sequence');
    sequence_part := LPAD(sequence_num::text, 4, '0');
    
    -- Combine all parts
    final_bench_id := first_part || last_part || date_part || sequence_part;
    
    RETURN final_bench_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate bench ID for candidates
CREATE OR REPLACE FUNCTION trigger_generate_bench_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.bench_id IS NULL OR NEW.bench_id = '' THEN
        NEW.bench_id := generate_bench_id(NEW.first_name, NEW.last_name);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER candidates_generate_bench_id
    BEFORE INSERT ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION trigger_generate_bench_id();

-- Function to update search vectors
CREATE OR REPLACE FUNCTION update_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_TABLE_NAME = 'candidates' THEN
        NEW.search_vector := 
            setweight(to_tsvector('english', COALESCE(NEW.first_name, '')), 'A') ||
            setweight(to_tsvector('english', COALESCE(NEW.last_name, '')), 'A') ||
            setweight(to_tsvector('english', COALESCE(NEW.current_title, '')), 'B') ||
            setweight(to_tsvector('english', COALESCE(NEW.current_company, '')), 'B') ||
            setweight(to_tsvector('english', COALESCE(array_to_string(NEW.primary_skills, ' '), '')), 'A') ||
            setweight(to_tsvector('english', COALESCE(array_to_string(NEW.secondary_skills, ' '), '')), 'C') ||
            setweight(to_tsvector('english', COALESCE(NEW.current_location, '')), 'C');
    ELSIF TG_TABLE_NAME = 'jobs' THEN
        NEW.search_vector := 
            setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||
            setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'B') ||
            setweight(to_tsvector('english', COALESCE(NEW.requirements, '')), 'B') ||
            setweight(to_tsvector('english', COALESCE(NEW.location, '')), 'C') ||
            setweight(to_tsvector('english', COALESCE(NEW.industry, '')), 'C');
    ELSIF TG_TABLE_NAME = 'documents' THEN
        NEW.search_vector := 
            setweight(to_tsvector('english', COALESCE(NEW.original_filename, '')), 'A') ||
            setweight(to_tsvector('english', COALESCE(NEW.extracted_text, '')), 'B');
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for search vector updates
CREATE TRIGGER candidates_search_vector_update
    BEFORE INSERT OR UPDATE ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION update_search_vector();

CREATE TRIGGER jobs_search_vector_update
    BEFORE INSERT OR UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_search_vector();

CREATE TRIGGER documents_search_vector_update
    BEFORE INSERT OR UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_search_vector();

-- Function to track activity logs
CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
DECLARE
    excluded_tables TEXT[] := ARRAY['activity_logs', 'search_analytics', 'notifications'];
    old_vals JSONB;
    new_vals JSONB;
    changed_fields TEXT[];
BEGIN
    -- Skip logging for certain tables
    IF TG_TABLE_NAME = ANY(excluded_tables) THEN
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- Prepare values based on operation
    IF TG_OP = 'DELETE' THEN
        old_vals := to_jsonb(OLD);
        new_vals := NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        old_vals := to_jsonb(OLD);
        new_vals := to_jsonb(NEW);
        -- Find changed fields
        SELECT ARRAY_AGG(key) INTO changed_fields
        FROM jsonb_each(old_vals) old_kv
        JOIN jsonb_each(new_vals) new_kv ON old_kv.key = new_kv.key
        WHERE old_kv.value <> new_kv.value;
    ELSE -- INSERT
        old_vals := NULL;
        new_vals := to_jsonb(NEW);
    END IF;

    -- Insert activity log
    INSERT INTO activity_logs (
        company_id,
        action,
        entity_type,
        entity_id,
        old_values,
        new_values,
        changed_fields,
        user_id
    ) VALUES (
        COALESCE(NEW.company_id, OLD.company_id),
        LOWER(TG_OP),
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        old_vals,
        new_vals,
        changed_fields,
        COALESCE(NEW.updated_by_id, NEW.created_by_id, OLD.updated_by_id, OLD.created_by_id)
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create activity logging triggers for main tables
CREATE TRIGGER activity_log_companies AFTER INSERT OR UPDATE OR DELETE ON companies FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_users AFTER INSERT OR UPDATE OR DELETE ON users FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_clients AFTER INSERT OR UPDATE OR DELETE ON clients FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_jobs AFTER INSERT OR UPDATE OR DELETE ON jobs FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_candidates AFTER INSERT OR UPDATE OR DELETE ON candidates FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_submissions AFTER INSERT OR UPDATE OR DELETE ON submissions FOR EACH ROW EXECUTE FUNCTION log_activity();
CREATE TRIGGER activity_log_placements AFTER INSERT OR UPDATE OR DELETE ON placements FOR EACH ROW EXECUTE FUNCTION log_activity();
-- =============================================================================
-- INITIAL DATA & PERMISSIONS
-- =============================================================================

-- Insert default subscription plans
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_users, max_jobs, max_candidates, features) VALUES
('starter', 49.00, 490.00, 5, 50, 500, '{"advanced_search": false, "ai_features": false, "api_access": false, "custom_reports": false}'),
('professional', 99.00, 990.00, 15, 200, 2000, '{"advanced_search": true, "ai_features": true, "api_access": false, "custom_reports": true}'),
('enterprise', 199.00, 1990.00, NULL, NULL, NULL, '{"advanced_search": true, "ai_features": true, "api_access": true, "custom_reports": true, "sso": true, "custom_fields": true}');

-- Insert default roles
INSERT INTO roles (name, description, permissions, is_system_role) VALUES
('Super Admin', 'Full system access across all companies', '{"all": true}', true),
('Company Admin', 'Full access within company', '{"company": {"all": true}}', false),
('Desk Manager', 'Manage desk operations and team', '{"desk": {"manage": true}, "users": {"view": true, "manage_desk": true}}', false),
('Senior Recruiter', 'Full recruitment operations', '{"candidates": {"all": true}, "jobs": {"all": true}, "submissions": {"all": true}, "interviews": {"all": true}, "communications": {"all": true}}', false),
('Recruiter', 'Standard recruitment operations', '{"candidates": {"view": true, "create": true, "edit_own": true}, "jobs": {"view": true, "edit_assigned": true}, "submissions": {"view_own": true, "create": true}, "interviews": {"view_own": true, "create": true}, "communications": {"view_own": true, "create": true}}', false),
('Bench Sales', 'Bench sales operations', '{"candidates": {"view": true, "create": true, "edit_own": true}, "submissions": {"view_own": true, "create": true}, "communications": {"view_own": true, "create": true}}', false),
('Viewer', 'Read-only access', '{"candidates": {"view": true}, "jobs": {"view": true}, "submissions": {"view": true}}', false);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all main tables
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE desks ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's company_id
CREATE OR REPLACE FUNCTION get_current_user_company_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT company_id 
        FROM users 
        WHERE id = auth.uid()
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has role
CREATE OR REPLACE FUNCTION user_has_role(role_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM users u 
        JOIN roles r ON u.role_id = r.id 
        WHERE u.id = auth.uid() 
        AND r.name = role_name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Company-level policies (users can only see their company's data)
CREATE POLICY "Users can view their company data" ON companies
    FOR SELECT USING (id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON desks
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON users
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON clients
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON jobs
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON candidates
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON documents
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON submissions
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON placements
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON interviews
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON communications
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON notifications
    FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Company data isolation" ON activity_logs
    FOR ALL USING (company_id = get_current_user_company_id());

-- =============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================================================

-- Additional indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_company_active ON users(company_id) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_candidates_owner_status ON candidates(owner_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_client_status ON jobs(client_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_entity ON activity_logs(entity_type, entity_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_status ON notifications(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_communications_created_at ON communications(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_candidates_desk_status_skills ON candidates(desk_id, status) INCLUDE (primary_skills);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_desk_status_priority ON jobs(desk_id, status, priority);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submissions_status_submitted_at ON submissions(status, submitted_at DESC);

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Active candidates view with computed fields
CREATE VIEW active_candidates AS
SELECT 
    c.*,
    u.first_name || ' ' || u.last_name AS owner_name,
    d.name AS desk_name,
    (SELECT COUNT(*) FROM submissions s WHERE s.candidate_id = c.id) AS submission_count,
    (SELECT MAX(submitted_at) FROM submissions s WHERE s.candidate_id = c.id) AS last_submission_date,
    (SELECT COUNT(*) FROM communications cm WHERE cm.candidate_id = c.id) AS communication_count,
    (SELECT MAX(created_at) FROM communications cm WHERE cm.candidate_id = c.id) AS last_communication_date
FROM candidates c
JOIN users u ON c.owner_id = u.id
JOIN desks d ON c.desk_id = d.id
WHERE c.status = 'active';

-- Active jobs view with computed fields
CREATE VIEW active_jobs AS
SELECT 
    j.*,
    cl.name AS client_name,
    u.first_name || ' ' || u.last_name AS recruiter_name,
    d.name AS desk_name,
    (SELECT COUNT(*) FROM submissions s WHERE s.job_id = j.id) AS submission_count,
    (SELECT COUNT(*) FROM submissions s WHERE s.job_id = j.id AND s.status IN ('interview', 'offer')) AS active_submission_count
FROM jobs j
JOIN clients cl ON j.client_id = cl.id
LEFT JOIN users u ON j.assigned_recruiter_id = u.id
JOIN desks d ON j.desk_id = d.id
WHERE j.status = 'open';

-- Dashboard metrics view
CREATE VIEW dashboard_metrics AS
SELECT 
    company_id,
    desk_id,
    COUNT(CASE WHEN entity_type = 'candidates' AND action = 'created' THEN 1 END) AS candidates_added_today,
    COUNT(CASE WHEN entity_type = 'jobs' AND action = 'created' THEN 1 END) AS jobs_added_today,
    COUNT(CASE WHEN entity_type = 'submissions' AND action = 'created' THEN 1 END) AS submissions_today,
    COUNT(CASE WHEN entity_type = 'placements' AND action = 'created' THEN 1 END) AS placements_today
FROM activity_logs 
WHERE created_at >= CURRENT_DATE
GROUP BY company_id, desk_id;

-- Comments
COMMENT ON TABLE companies IS 'Multi-tenant company/organization management';
COMMENT ON TABLE desks IS 'Desk-based role separation (Recruitment vs BenchSales)';
COMMENT ON TABLE candidates IS 'Candidate profiles with unique bench IDs and fraud prevention';
COMMENT ON TABLE documents IS 'Document management with version control and fraud detection';
COMMENT ON TABLE jobs IS 'Job postings with semantic search capabilities';
COMMENT ON TABLE submissions IS 'Candidate job submissions tracking';
COMMENT ON TABLE placements IS 'Successful placements with guarantee tracking';
COMMENT ON TABLE activity_logs IS 'Comprehensive audit trail for all actions';

-- Schema validation
DO $$
BEGIN
    ASSERT (SELECT COUNT(*) FROM subscription_plans) >= 3, 'Subscription plans not properly inserted';
    ASSERT (SELECT COUNT(*) FROM roles) >= 7, 'Default roles not properly inserted';
    RAISE NOTICE 'SourceFlex database schema validation completed successfully!';
END $$;
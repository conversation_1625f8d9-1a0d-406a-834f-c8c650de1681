-- Migration 006: Analytics, Metrics, and Activity Logging
-- Run this SIXTH (after 005_documents_communication.sql)

-- =============================================================================
-- METRICS AND ANALYTICS
-- =============================================================================

-- Company and desk performance metrics
CREATE TABLE metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID REFERENCES desks(id) ON DELETE CASCADE, -- NULL for company-wide metrics
    
    -- Time period
    metric_date DATE NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- daily, weekly, monthly, quarterly
    
    -- Core metrics
    candidates_added INTEGER DEFAULT 0,
    jobs_posted INTEGER DEFAULT 0,
    submissions_made INTEGER DEFAULT 0,
    interviews_scheduled INTEGER DEFAULT 0,
    placements_made INTEGER DEFAULT 0,
    
    -- Performance metrics
    submission_to_interview_rate DECIMAL(5,4), -- Percentage
    interview_to_placement_rate DECIMAL(5,4), -- Percentage
    average_time_to_placement_days DECIMAL(8,2),
    
    -- Financial metrics (in cents)
    total_fees_earned INTEGER DEFAULT 0,
    average_placement_fee INTEGER DEFAULT 0,
    revenue_per_placement INTEGER DEFAULT 0,
    
    -- Activity metrics
    calls_made INTEGER DEFAULT 0,
    emails_sent INTEGER DEFAULT 0,
    documents_uploaded INTEGER DEFAULT 0,
    
    -- Search and engagement
    searches_performed INTEGER DEFAULT 0,
    profile_views INTEGER DEFAULT 0,
    job_views INTEGER DEFAULT 0,
    
    -- User activity
    active_users INTEGER DEFAULT 0,
    login_count INTEGER DEFAULT 0,
    session_duration_minutes INTEGER DEFAULT 0,
    
    -- Additional metrics as JSON
    custom_metrics JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(company_id, desk_id, metric_date, metric_type)
);

-- Search analytics for improving search functionality
CREATE TABLE search_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Search details
    search_type VARCHAR(20) NOT NULL, -- candidate, job, client, global
    search_query TEXT NOT NULL,
    search_filters JSONB DEFAULT '{}', -- Applied filters
    
    -- Results
    results_count INTEGER NOT NULL,
    results_shown INTEGER DEFAULT 10, -- How many were displayed
    search_duration_ms INTEGER, -- Query execution time
    
    -- User interaction
    clicked_results INTEGER[] DEFAULT '{}', -- Indices of clicked results
    result_clicked BOOLEAN DEFAULT false,
    click_position INTEGER, -- Position of first clicked result
    
    -- Search quality
    no_results BOOLEAN DEFAULT false,
    refined_search BOOLEAN DEFAULT false, -- Did user refine this search?
    
    -- Context
    page_context VARCHAR(50), -- Where search was performed
    session_id VARCHAR(255), -- User session identifier
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Saved searches for users
CREATE TABLE saved_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Search configuration
    name VARCHAR(255) NOT NULL,
    description TEXT,
    search_type VARCHAR(20) NOT NULL, -- candidate, job, client
    
    -- Search parameters
    search_query TEXT,
    filters JSONB NOT NULL DEFAULT '{}',
    sort_by VARCHAR(50) DEFAULT 'relevance',
    sort_order VARCHAR(4) DEFAULT 'desc', -- asc, desc
    
    -- Automation
    is_alert BOOLEAN DEFAULT false, -- Send notifications for new matches
    alert_frequency VARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly
    last_alert_sent TIMESTAMP WITH TIME ZONE,
    
    -- Usage tracking
    last_executed TIMESTAMP WITH TIME ZONE,
    execution_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ACTIVITY LOGGING
-- =============================================================================

-- Company-level activity logs
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for system actions
    
    -- Action details
    action VARCHAR(100) NOT NULL, -- create_candidate, update_job, etc.
    entity_type VARCHAR(50) NOT NULL, -- candidate, job, client, user, etc.
    entity_id UUID, -- ID of the affected entity
    
    -- Change tracking
    old_values JSONB, -- Previous values (for updates)
    new_values JSONB, -- New values (for creates/updates)
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255), -- For correlating related actions
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}',
    description TEXT, -- Human-readable description
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification system
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Notification content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL, -- info, success, warning, error, reminder
    
    -- Priority and categorization
    priority VARCHAR(10) DEFAULT 'normal', -- low, normal, high, urgent
    category VARCHAR(50) DEFAULT 'general', -- general, candidate, job, placement, system
    
    -- Related entities
    entity_type VARCHAR(50), -- candidate, job, client, etc.
    entity_id UUID,
    
    -- Action links
    action_url TEXT, -- URL to navigate to
    action_text VARCHAR(100), -- Button/link text
    
    -- Status
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Delivery
    delivery_method VARCHAR(20) DEFAULT 'in_app', -- in_app, email, sms
    sent_via_email BOOLEAN DEFAULT false,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Scheduling
    scheduled_for TIMESTAMP WITH TIME ZONE, -- NULL for immediate
    expires_at TIMESTAMP WITH TIME ZONE, -- When notification becomes irrelevant
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ANALYTICS FUNCTIONS
-- =============================================================================

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_activity(
    company_id_param UUID,
    user_id_param UUID,
    action_param VARCHAR(100),
    entity_type_param VARCHAR(50),
    entity_id_param UUID DEFAULT NULL,
    old_values_param JSONB DEFAULT NULL,
    new_values_param JSONB DEFAULT NULL,
    description_param TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO activity_logs (
        company_id,
        user_id,
        action,
        entity_type,
        entity_id,
        old_values,
        new_values,
        description
    ) VALUES (
        company_id_param,
        user_id_param,
        action_param,
        entity_type_param,
        entity_id_param,
        old_values_param,
        new_values_param,
        description_param
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    company_id_param UUID,
    user_id_param UUID,
    title_param VARCHAR(255),
    message_param TEXT,
    notification_type_param VARCHAR(50) DEFAULT 'info',
    entity_type_param VARCHAR(50) DEFAULT NULL,
    entity_id_param UUID DEFAULT NULL,
    action_url_param TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notifications (
        company_id,
        user_id,
        title,
        message,
        notification_type,
        entity_type,
        entity_id,
        action_url
    ) VALUES (
        company_id_param,
        user_id_param,
        title_param,
        message_param,
        notification_type_param,
        entity_type_param,
        entity_id_param,
        action_url_param
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update daily metrics
CREATE OR REPLACE FUNCTION update_daily_metrics(
    target_date DATE DEFAULT CURRENT_DATE,
    company_id_param UUID DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    company_record RECORD;
    desk_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- If company_id is provided, only update that company
    -- Otherwise update all companies
    FOR company_record IN 
        SELECT id FROM companies 
        WHERE company_id_param IS NULL OR id = company_id_param
    LOOP
        -- Update company-wide metrics
        INSERT INTO metrics (
            company_id,
            desk_id,
            metric_date,
            metric_type,
            candidates_added,
            jobs_posted,
            submissions_made,
            interviews_scheduled,
            placements_made
        )
        SELECT 
            company_record.id,
            NULL, -- Company-wide
            target_date,
            'daily',
            (SELECT COUNT(*) FROM candidates 
             WHERE company_id = company_record.id 
             AND DATE(created_at) = target_date),
            (SELECT COUNT(*) FROM jobs 
             WHERE company_id = company_record.id 
             AND DATE(created_at) = target_date),
            (SELECT COUNT(*) FROM submissions s
             JOIN candidates c ON s.candidate_id = c.id
             WHERE c.company_id = company_record.id 
             AND DATE(s.created_at) = target_date),
            (SELECT COUNT(*) FROM interviews i
             JOIN candidates c ON i.candidate_id = c.id
             WHERE c.company_id = company_record.id 
             AND DATE(i.created_at) = target_date),
            (SELECT COUNT(*) FROM placements p
             WHERE p.company_id = company_record.id 
             AND DATE(p.created_at) = target_date)
        ON CONFLICT (company_id, desk_id, metric_date, metric_type) 
        DO UPDATE SET 
            candidates_added = EXCLUDED.candidates_added,
            jobs_posted = EXCLUDED.jobs_posted,
            submissions_made = EXCLUDED.submissions_made,
            interviews_scheduled = EXCLUDED.interviews_scheduled,
            placements_made = EXCLUDED.placements_made,
            updated_at = NOW();
            
        updated_count := updated_count + 1;
        
        -- Update desk-specific metrics
        FOR desk_record IN 
            SELECT id FROM desks WHERE company_id = company_record.id
        LOOP
            INSERT INTO metrics (
                company_id,
                desk_id,
                metric_date,
                metric_type,
                candidates_added,
                jobs_posted,
                submissions_made,
                interviews_scheduled,
                placements_made
            )
            SELECT 
                company_record.id,
                desk_record.id,
                target_date,
                'daily',
                (SELECT COUNT(*) FROM candidates 
                 WHERE company_id = company_record.id 
                 AND desk_id = desk_record.id
                 AND DATE(created_at) = target_date),
                (SELECT COUNT(*) FROM jobs 
                 WHERE company_id = company_record.id 
                 AND desk_id = desk_record.id
                 AND DATE(created_at) = target_date),
                (SELECT COUNT(*) FROM submissions s
                 JOIN candidates c ON s.candidate_id = c.id
                 WHERE c.company_id = company_record.id 
                 AND c.desk_id = desk_record.id
                 AND DATE(s.created_at) = target_date),
                (SELECT COUNT(*) FROM interviews i
                 JOIN candidates c ON i.candidate_id = c.id
                 WHERE c.company_id = company_record.id 
                 AND c.desk_id = desk_record.id
                 AND DATE(i.created_at) = target_date),
                (SELECT COUNT(*) FROM placements p
                 JOIN candidates c ON p.candidate_id = c.id
                 WHERE c.company_id = company_record.id 
                 AND c.desk_id = desk_record.id
                 AND DATE(p.created_at) = target_date)
            ON CONFLICT (company_id, desk_id, metric_date, metric_type) 
            DO UPDATE SET 
                candidates_added = EXCLUDED.candidates_added,
                jobs_posted = EXCLUDED.jobs_posted,
                submissions_made = EXCLUDED.submissions_made,
                interviews_scheduled = EXCLUDED.interviews_scheduled,
                placements_made = EXCLUDED.placements_made,
                updated_at = NOW();
                
            updated_count := updated_count + 1;
        END LOOP;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
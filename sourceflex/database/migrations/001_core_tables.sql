-- Migration 001: Core Extensions and Basic Tables
-- Run this FIRST

-- Enable required extensions (skip pgvector for now)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
-- Note: pgvector requires superuser, will add semantic search later

-- =============================================================================
-- CORE SUBSCRIPTION MANAGEMENT
-- =============================================================================

-- Subscription plans and features
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE, -- starter, professional, enterprise
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2) NOT NULL,
    max_users INTEGER,
    max_jobs INTEGER,
    max_candidates INTEGER,
    features JSONB NOT NULL DEFAULT '{}', -- Feature flags
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Stripe integration fields
    stripe_product_id VARCHAR(255), -- Stripe product ID
    stripe_price_id_monthly VARCHAR(255), -- Stripe price ID for monthly
    stripe_price_id_yearly VARCHAR(255), -- Stripe price ID for yearly
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- PLATFORM ADMINISTRATION (SourceFlex Team)
-- =============================================================================

-- Platform admins table (SourceFlex team members)
CREATE TABLE platform_admins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'platform_admin', -- platform_admin, super_admin
    permissions JSONB NOT NULL DEFAULT '{}', -- Platform-level permissions
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization approval workflow
CREATE TABLE organization_approval_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    admin_email VARCHAR(255) NOT NULL,
    admin_first_name VARCHAR(100) NOT NULL,
    admin_last_name VARCHAR(100) NOT NULL,
    requested_plan_id UUID REFERENCES subscription_plans(id),
    
    -- Request details
    company_size VARCHAR(50),
    industry VARCHAR(100),
    use_case TEXT,
    
    -- Approval workflow
    status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, review
    reviewed_by UUID REFERENCES platform_admins(id),
    review_notes TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Detection flags
    is_existing_domain BOOLEAN DEFAULT false,
    duplicate_check_results JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company/Tenant management
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE, -- For SSO/domain verification
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    subscription_status VARCHAR(20) DEFAULT 'trial', -- trial, active, suspended, cancelled
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Stripe integration fields
    stripe_customer_id VARCHAR(255) UNIQUE, -- Stripe customer ID
    stripe_subscription_id VARCHAR(255), -- Current Stripe subscription ID
    
    -- Approval workflow connection
    approval_request_id UUID REFERENCES organization_approval_requests(id),
    approved_by UUID REFERENCES platform_admins(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Subscription limits tracking
    current_users INTEGER DEFAULT 0,
    current_jobs INTEGER DEFAULT 0,
    current_candidates INTEGER DEFAULT 0,
    
    -- Company settings
    settings JSONB DEFAULT '{}',
    logo_url TEXT,
    website VARCHAR(255),
    industry VARCHAR(100),
    size_range VARCHAR(50) -- 1-10, 11-50, 51-200, etc.
);

-- =============================================================================
-- DESK AND ROLE MANAGEMENT
-- =============================================================================

-- Desk types within companies (Recruitment vs BenchSales)
CREATE TABLE desks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL, -- "Recruitment", "Bench Sales", etc.
    type VARCHAR(20) NOT NULL CHECK (type IN ('recruitment', 'bench_sales')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(company_id, name)
);

-- Role definitions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_system_role BOOLEAN DEFAULT false, -- System roles cannot be deleted
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID REFERENCES desks(id) ON DELETE SET NULL,
    role_id UUID NOT NULL REFERENCES roles(id),
    
    -- User status and settings
    is_active BOOLEAN NOT NULL DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

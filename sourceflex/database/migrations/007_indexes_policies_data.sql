-- Migration 007: Indexes, RLS Policies, Views, and Data
-- Run this SEVENTH (after 006_metrics_analytics.sql)

-- =============================================================================
-- PERFORMANCE INDEXES
-- =============================================================================

-- Company and tenant indexes
CREATE INDEX idx_companies_domain ON companies(domain);
CREATE INDEX idx_companies_subscription_status ON companies(subscription_status);
CREATE INDEX idx_companies_stripe_customer_id ON companies(stripe_customer_id);

-- Platform admin indexes
CREATE INDEX idx_platform_admins_email ON platform_admins(email);
CREATE INDEX idx_platform_admins_active ON platform_admins(is_active);

-- Organization approval indexes
CREATE INDEX idx_org_approval_status ON organization_approval_requests(status);
CREATE INDEX idx_org_approval_domain ON organization_approval_requests(domain);
CREATE INDEX idx_org_approval_created ON organization_approval_requests(created_at);

-- User and role indexes
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_desk_id ON users(desk_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);

-- Candidate indexes
CREATE INDEX idx_candidates_company_id ON candidates(company_id);
CREATE INDEX idx_candidates_desk_id ON candidates(desk_id);
CREATE INDEX idx_candidates_bench_id ON candidates(bench_id);
CREATE INDEX idx_candidates_availability_status ON candidates(availability_status);
CREATE INDEX idx_candidates_search_vector ON candidates USING gin(search_vector);
CREATE INDEX idx_candidates_ssn_last_four ON candidates(ssn_last_four);

-- Job indexes
CREATE INDEX idx_jobs_company_id ON jobs(company_id);
CREATE INDEX idx_jobs_desk_id ON jobs(desk_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_search_vector ON jobs USING gin(search_vector);

-- Submission and placement indexes
CREATE INDEX idx_submissions_candidate_id ON submissions(candidate_id);
CREATE INDEX idx_submissions_job_id ON submissions(job_id);
CREATE INDEX idx_submissions_status ON submissions(status);
CREATE INDEX idx_placements_company_id ON placements(company_id);
CREATE INDEX idx_placements_status ON placements(status);

-- Stripe-related indexes
CREATE INDEX idx_stripe_customers_stripe_id ON stripe_customers(stripe_customer_id);
CREATE INDEX idx_stripe_subscriptions_stripe_id ON stripe_subscriptions(stripe_subscription_id);
CREATE INDEX idx_stripe_subscriptions_company ON stripe_subscriptions(company_id);
CREATE INDEX idx_stripe_invoices_stripe_id ON stripe_invoices(stripe_invoice_id);
CREATE INDEX idx_stripe_webhook_events_type ON stripe_webhook_events(event_type);
CREATE INDEX idx_stripe_webhook_events_processed ON stripe_webhook_events(processed);

-- Document indexes
CREATE INDEX idx_documents_candidate_id ON documents(candidate_id);
CREATE INDEX idx_documents_file_hash ON documents(file_hash);
CREATE INDEX idx_documents_content_hash ON documents(content_hash);
CREATE INDEX idx_documents_search_vector ON documents USING gin(search_vector);

-- Communication indexes
CREATE INDEX idx_message_threads_company_id ON message_threads(company_id);
CREATE INDEX idx_messages_thread_id ON messages(thread_id);
CREATE INDEX idx_messages_search_vector ON messages USING gin(search_vector);

-- Activity log indexes
CREATE INDEX idx_activity_logs_company_id ON activity_logs(company_id);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_platform_activity_logs_admin_id ON platform_activity_logs(admin_id);
CREATE INDEX idx_platform_activity_logs_created_at ON platform_activity_logs(created_at);

-- Metrics and analytics indexes
CREATE INDEX idx_metrics_company_id ON metrics(company_id);
CREATE INDEX idx_metrics_date_type ON metrics(metric_date, metric_type);
CREATE INDEX idx_search_analytics_company_id ON search_analytics(company_id);
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Helper function for current user's company ID (placeholder)
CREATE OR REPLACE FUNCTION get_current_user_company_id()
RETURNS UUID AS $$
BEGIN
    -- This will be implemented based on nHost auth integration
    -- For now, returning NULL as placeholder
    RETURN NULL::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable RLS on all tenant tables
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE desks ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_searches ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Company isolation policies
CREATE POLICY "Company isolation" ON companies 
FOR ALL USING (id = get_current_user_company_id());

CREATE POLICY "Desk isolation" ON desks 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "User isolation" ON users 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Candidate isolation" ON candidates 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Job isolation" ON jobs 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Client isolation" ON clients 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Submission isolation" ON submissions 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Placement isolation" ON placements 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Interview isolation" ON interviews 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Document isolation" ON documents 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Message thread isolation" ON message_threads 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Message isolation" ON messages 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Metrics isolation" ON metrics 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Activity log isolation" ON activity_logs 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Search analytics isolation" ON search_analytics 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Saved search isolation" ON saved_searches 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Notification isolation" ON notifications 
FOR ALL USING (company_id = get_current_user_company_id());

-- Stripe tables - company isolation
ALTER TABLE stripe_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Stripe customer isolation" ON stripe_customers 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Stripe subscription isolation" ON stripe_subscriptions 
FOR ALL USING (company_id = get_current_user_company_id());

CREATE POLICY "Stripe invoice isolation" ON stripe_invoices 
FOR ALL USING (company_id = get_current_user_company_id());

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Organization approval dashboard view
CREATE VIEW organization_approval_dashboard AS
SELECT 
    oar.*,
    pa.first_name || ' ' || pa.last_name AS reviewed_by_name,
    sp.name AS requested_plan_name,
    sp.price_monthly,
    CASE 
        WHEN oar.status = 'pending' THEN EXTRACT(EPOCH FROM (NOW() - oar.created_at))/3600
        ELSE NULL 
    END AS hours_pending
FROM organization_approval_requests oar
LEFT JOIN platform_admins pa ON oar.reviewed_by = pa.id
LEFT JOIN subscription_plans sp ON oar.requested_plan_id = sp.id;

-- Company metrics summary view
CREATE VIEW company_metrics_summary AS
SELECT 
    c.id,
    c.name,
    c.subscription_status,
    sp.name AS plan_name,
    c.current_users,
    c.current_jobs,
    c.current_candidates,
    sp.max_users,
    sp.max_jobs,
    sp.max_candidates,
    CASE 
        WHEN sp.max_users IS NOT NULL THEN (c.current_users::FLOAT / sp.max_users * 100)
        ELSE NULL 
    END AS user_utilization_percentage,
    CASE 
        WHEN sp.max_candidates IS NOT NULL THEN (c.current_candidates::FLOAT / sp.max_candidates * 100)
        ELSE NULL 
    END AS candidate_utilization_percentage
FROM companies c
LEFT JOIN subscription_plans sp ON c.subscription_plan_id = sp.id;

-- Active candidates with computed metrics
CREATE VIEW active_candidates AS
SELECT 
    c.*,
    u.first_name || ' ' || u.last_name AS recruiter_name,
    d.name AS desk_name,
    d.type AS desk_type,
    COALESCE(sub_counts.submission_count, 0) AS submission_count,
    COALESCE(int_counts.interview_count, 0) AS interview_count,
    CASE 
        WHEN c.availability_status = 'available' AND c.availability_date <= CURRENT_DATE 
        THEN true 
        ELSE false 
    END AS is_immediately_available
FROM candidates c
LEFT JOIN users u ON c.recruiter_id = u.id
LEFT JOIN desks d ON c.desk_id = d.id
LEFT JOIN (
    SELECT candidate_id, COUNT(*) AS submission_count
    FROM submissions
    GROUP BY candidate_id
) sub_counts ON c.id = sub_counts.candidate_id
LEFT JOIN (
    SELECT candidate_id, COUNT(*) AS interview_count
    FROM interviews
    GROUP BY candidate_id
) int_counts ON c.id = int_counts.candidate_id
WHERE c.availability_status != 'not_available';

-- =============================================================================
-- DEFAULT DATA INSERTION
-- =============================================================================

-- Insert default subscription plans
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_users, max_jobs, max_candidates, features) VALUES
('starter', 99.00, 990.00, 5, 25, 100, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "email_support": true,
    "api_access": false,
    "advanced_analytics": false,
    "priority_support": false,
    "custom_integrations": false
}'),
('professional', 299.00, 2990.00, 25, 100, 500, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "advanced_analytics": true,
    "email_support": true,
    "phone_support": true,
    "api_access": true,
    "priority_support": false,
    "custom_integrations": false,
    "bulk_operations": true
}'),
('enterprise', 999.00, 9990.00, NULL, NULL, NULL, '{
    "semantic_search": true,
    "document_management": true,
    "basic_analytics": true,
    "advanced_analytics": true,
    "custom_analytics": true,
    "email_support": true,
    "phone_support": true,
    "priority_support": true,
    "api_access": true,
    "custom_integrations": true,
    "bulk_operations": true,
    "white_label": true,
    "dedicated_account_manager": true
}');

-- Insert default roles
INSERT INTO roles (name, description, permissions, is_system_role) VALUES
('org_admin', 'Organization Administrator', '{
    "manage_users": true,
    "manage_desks": true,
    "manage_billing": true,
    "view_analytics": true,
    "manage_settings": true,
    "manage_integrations": true
}', true),
('recruiter', 'Recruiter', '{
    "manage_candidates": true,
    "manage_jobs": true,
    "manage_submissions": true,
    "view_analytics": false,
    "manage_clients": true
}', true),
('bench_sales', 'Bench Sales Representative', '{
    "view_candidates": true,
    "manage_placements": true,
    "manage_clients": true,
    "view_analytics": false
}', true),
('sales_manager', 'Sales Manager', '{
    "manage_candidates": true,
    "manage_jobs": true,
    "manage_submissions": true,
    "manage_placements": true,
    "manage_clients": true,
    "view_analytics": true,
    "manage_team": true
}', true),
('account_manager', 'Account Manager', '{
    "manage_clients": true,
    "view_candidates": true,
    "view_jobs": true,
    "view_submissions": true,
    "view_placements": true,
    "view_analytics": false
}', true);

-- Insert default platform admin (to be updated with actual admin details)
INSERT INTO platform_admins (email, first_name, last_name, role, permissions) VALUES
('<EMAIL>', 'System', 'Administrator', 'super_admin', '{
    "manage_organizations": true,
    "manage_subscriptions": true,
    "manage_platform_settings": true,
    "view_all_analytics": true,
    "manage_platform_admins": true,
    "system_maintenance": true
}');

-- =============================================================================
-- COMMENTS
-- =============================================================================

COMMENT ON TABLE platform_admins IS 'SourceFlex platform administrators (internal team)';
COMMENT ON TABLE organization_approval_requests IS 'Manual approval workflow for new organizations';
COMMENT ON TABLE companies IS 'Multi-tenant company/organization management with Stripe integration';
COMMENT ON TABLE stripe_customers IS 'Stripe customer data synchronized from Stripe';
COMMENT ON TABLE stripe_subscriptions IS 'Active Stripe subscriptions tracking';
COMMENT ON TABLE stripe_invoices IS 'Stripe invoice tracking and status';
COMMENT ON TABLE stripe_webhook_events IS 'Stripe webhook event processing log';
COMMENT ON TABLE candidates IS 'Candidate profiles with Bench ID system (FFF+LLL+MMDD+SSSS+XX)';
COMMENT ON TABLE documents IS 'Document management with version control and fraud detection';
COMMENT ON TABLE jobs IS 'Job postings with semantic search capabilities';
COMMENT ON TABLE submissions IS 'Candidate job submissions tracking';
COMMENT ON TABLE placements IS 'Successful placements with guarantee tracking';
COMMENT ON TABLE activity_logs IS 'Comprehensive audit trail for company actions';
COMMENT ON TABLE platform_activity_logs IS 'Platform admin action audit trail';

-- Schema validation
DO $$
BEGIN
    ASSERT (SELECT COUNT(*) FROM subscription_plans) >= 3, 'Subscription plans not properly inserted';
    ASSERT (SELECT COUNT(*) FROM roles) >= 5, 'Default roles not properly inserted';
    ASSERT (SELECT COUNT(*) FROM platform_admins) >= 1, 'Platform admin not properly inserted';
    RAISE NOTICE 'SourceFlex database schema completed successfully!';
    RAISE NOTICE 'All 7 migration files have been applied.';
END $$;
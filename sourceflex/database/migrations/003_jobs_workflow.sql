-- Migration 003: Jobs, Submissions, and Business Logic
-- Run this THIRD (after 002_candidates_bench.sql)

-- =============================================================================
-- JOBS AND WORKFLOW MANAGEMENT
-- =============================================================================

-- Job postings
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    
    -- Basic job information
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    
    -- Job details
    job_type VARCHAR(20) NOT NULL CHECK (job_type IN ('permanent', 'contract', 'both')),
    experience_level VARCHAR(20), -- junior, mid, senior, principal
    remote_option VARCHAR(20) DEFAULT 'hybrid', -- remote, hybrid, onsite
    
    -- Compensation
    salary_min DECIMAL(12,2),
    salary_max DECIMAL(12,2),
    salary_currency VARCHAR(3) DEFAULT 'USD',
    hourly_rate DECIMAL(8,2),
    
    -- Location
    location VARCHAR(255) NOT NULL,
    timezone VARCHAR(50),
    
    -- Job status
    status VARCHAR(20) DEFAULT 'open', -- open, closed, on_hold, filled
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent
    
    -- Client information
    client_id UUID, -- Will reference clients table when created
    end_client VARCHAR(255), -- If different from direct client
    
    -- Skills and requirements
    required_skills TEXT[] NOT NULL,
    preferred_skills TEXT[],
    
    -- Search and matching
    search_vector tsvector,
    -- embedding vector(1536), -- Semantic search embeddings (requires pgvector)
    
    -- Metadata
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client companies
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Client information
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    industry VARCHAR(100),
    size_range VARCHAR(50),
    
    -- Contact information
    primary_contact_name VARCHAR(255),
    primary_contact_email VARCHAR(255),
    primary_contact_phone VARCHAR(20),
    address TEXT,
    
    -- Relationship details
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, prospect, closed
    tier VARCHAR(20) DEFAULT 'standard', -- prospect, standard, preferred, strategic
    payment_terms VARCHAR(50), -- net-30, net-45, etc.
    
    -- Settings
    preferred_markup_percentage DECIMAL(5,2),
    settings JSONB DEFAULT '{}',
    
    -- Audit
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add the foreign key constraint for jobs.client_id now that clients table exists
ALTER TABLE jobs ADD CONSTRAINT fk_jobs_client_id FOREIGN KEY (client_id) REFERENCES clients(id);

-- Candidate job submissions
CREATE TABLE submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL REFERENCES candidates(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    
    -- Submission details
    status VARCHAR(20) DEFAULT 'submitted', -- submitted, reviewed, shortlisted, interview_scheduled, rejected, placed
    submitted_by UUID NOT NULL REFERENCES users(id),
    submission_notes TEXT,
    
    -- Rate information
    proposed_rate DECIMAL(8,2),
    client_rate DECIMAL(8,2),
    markup_percentage DECIMAL(5,2),
    
    -- Feedback and progress
    client_feedback TEXT,
    internal_notes TEXT,
    
    -- Timeline
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_status_change TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(candidate_id, job_id) -- Prevent duplicate submissions
);

-- Successful placements
CREATE TABLE placements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL REFERENCES candidates(id),
    job_id UUID NOT NULL REFERENCES jobs(id),
    
    -- Placement details
    start_date DATE NOT NULL,
    end_date DATE, -- For contracts
    placement_type VARCHAR(20) NOT NULL, -- permanent, contract
    
    -- Financial details
    final_rate DECIMAL(8,2),
    client_rate DECIMAL(8,2),
    markup_amount DECIMAL(8,2),
    commission_percentage DECIMAL(5,2),
    
    -- Guarantee and tracking
    guarantee_period_days INTEGER DEFAULT 90,
    guarantee_expires_at DATE,
    replacement_count INTEGER DEFAULT 0,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'active', -- active, completed, terminated, replaced
    termination_reason TEXT,
    
    -- Audit
    placed_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- INTERVIEWS
-- =============================================================================

-- Interview scheduling
CREATE TABLE interviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL REFERENCES candidates(id),
    job_id UUID NOT NULL REFERENCES jobs(id),
    
    -- Interview details
    title VARCHAR(255) NOT NULL,
    description TEXT,
    interview_type VARCHAR(20) DEFAULT 'video', -- phone, video, in_person, technical
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    timezone VARCHAR(50),
    
    -- Meeting details
    meeting_link TEXT,
    meeting_password VARCHAR(100),
    location TEXT, -- For in-person interviews
    
    -- Participants
    interviewer_name VARCHAR(255),
    interviewer_email VARCHAR(255),
    panel_members TEXT[], -- Array of interviewer names
    
    -- Status and results
    status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, completed, cancelled, rescheduled
    result VARCHAR(20), -- pass, fail, pending, no_show
    feedback TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    
    -- Next steps
    next_round BOOLEAN DEFAULT false,
    follow_up_notes TEXT,
    
    -- Audit
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to update search vectors for jobs
CREATE OR REPLACE FUNCTION update_job_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.title, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.requirements, '') || ' ' ||
        COALESCE(array_to_string(NEW.required_skills, ' '), '') || ' ' ||
        COALESCE(array_to_string(NEW.preferred_skills, ' '), '') || ' ' ||
        COALESCE(NEW.location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER jobs_search_vector_update
    BEFORE INSERT OR UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_job_search_vector();

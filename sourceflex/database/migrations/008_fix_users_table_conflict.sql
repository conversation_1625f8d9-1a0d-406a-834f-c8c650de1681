-- Migration 008: Fix Users Table Conflict with Auth.Users
-- Run this to resolve Hasura tracking conflicts
-- CRITICAL: This fixes the GraphQL mutation conflict between public.users and auth.users

-- =============================================================================
-- STEP 1: RENAME USERS TABLE AND REMOVE DUPLICATE FIELDS
-- =============================================================================

-- First, disable RLS temporarily to avoid policy conflicts during migration
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Drop existing policies that reference users table
DROP POLICY IF EXISTS "User isolation" ON users;

-- Rename users table to user_profiles to avoid conflict with auth.users
ALTER TABLE users RENAME TO user_profiles;

-- Remove duplicate fields that already exist in auth.users
-- Keep the auth user ID as reference, remove duplicate email and names
ALTER TABLE user_profiles 
DROP COLUMN IF EXISTS email,
DROP COLUMN IF EXISTS first_name, 
DROP COLUMN IF EXISTS last_name,
DROP COLUMN IF EXISTS email_verified,
DROP COLUMN IF EXISTS last_login_at;

-- Add comment to clarify the relationship
COMMENT ON TABLE user_profiles IS 'Business profile data for authenticated users - id references auth.users.id';
COMMENT ON COLUMN user_profiles.id IS 'References auth.users.id from nHost authentication';

-- =============================================================================
-- STEP 2: UPDATE FOREIGN KEY REFERENCES (ONLY FOR EXISTING TABLES)
-- =============================================================================

-- Update candidates table foreign key constraint (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'candidates' AND table_schema = 'public') THEN
        ALTER TABLE candidates 
        DROP CONSTRAINT IF EXISTS candidates_recruiter_id_fkey;
        
        -- Only add constraint if recruiter_id column exists
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'candidates' AND column_name = 'recruiter_id') THEN
            ALTER TABLE candidates
            ADD CONSTRAINT candidates_recruiter_id_fkey 
                FOREIGN KEY (recruiter_id) REFERENCES user_profiles(id);
        END IF;
    END IF;
END $$;

-- =============================================================================
-- STEP 3: UPDATE INDEXES
-- =============================================================================

-- Update index names to reference user_profiles
DROP INDEX IF EXISTS idx_users_company_id;
DROP INDEX IF EXISTS idx_users_desk_id;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_active;

-- Create new indexes for user_profiles
CREATE INDEX idx_user_profiles_company_id ON user_profiles(company_id);
CREATE INDEX idx_user_profiles_desk_id ON user_profiles(desk_id);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);

-- =============================================================================
-- STEP 4: UPDATE RLS POLICIES
-- =============================================================================

-- Re-enable RLS and create updated policy
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create new RLS policy for user_profiles (only if function exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_current_user_company_id') THEN
        EXECUTE 'CREATE POLICY "User profile isolation" ON user_profiles FOR ALL USING (company_id = get_current_user_company_id())';
    ELSE
        -- Create a temporary policy that allows all access until function is implemented
        EXECUTE 'CREATE POLICY "User profile isolation" ON user_profiles FOR ALL USING (true)';
    END IF;
END $$;

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Verify the migration worked correctly
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    -- Check that user_profiles table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'user_profiles' AND table_schema = 'public'
    ) INTO table_exists;
    
    IF NOT table_exists THEN
        RAISE EXCEPTION 'Migration failed: user_profiles table does not exist';
    END IF;
    
    -- Check that old users table no longer exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'users' AND table_schema = 'public'
    ) INTO table_exists;
    
    IF table_exists THEN
        RAISE EXCEPTION 'Migration failed: old users table still exists';
    END IF;
    
    RAISE NOTICE 'Migration 008 completed successfully!';
    RAISE NOTICE 'Users table renamed to user_profiles';
    RAISE NOTICE 'You can now track all tables in Hasura without conflicts.';
END $$;

-- Migration 004: Stripe Integration and Payment Management
-- Run this FOURTH (after 003_jobs_workflow.sql)

-- =============================================================================
-- STRIPE INTEGRATION TABLES
-- =============================================================================

-- Stripe customers synchronized from Stripe
CREATE TABLE stripe_customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    stripe_customer_id VARCHAR(255) NOT NULL UNIQUE, -- Stripe customer ID
    
    -- Customer data from Stripe
    email VARCHAR(255),
    name VARCHAR(255),
    description TEXT,
    
    -- Billing details
    address JSONB, -- Stripe address object
    phone VARCHAR(50),
    tax_ids JSONB DEFAULT '[]', -- Array of tax ID objects
    
    -- Payment methods
    default_payment_method VARCHAR(255), -- Stripe payment method ID
    invoice_settings JSONB DEFAULT '{}', -- Stripe invoice settings
    
    -- Status and metadata
    delinquent BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}', -- Custom metadata from Stripe
    
    -- Sync tracking
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stripe subscriptions tracking
CREATE TABLE stripe_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255) NOT NULL UNIQUE, -- Stripe subscription ID
    stripe_customer_id VARCHAR(255) NOT NULL, -- References stripe_customers
    
    -- Subscription details
    status VARCHAR(50) NOT NULL, -- active, canceled, incomplete, etc.
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    
    -- Pricing information
    plan_id UUID REFERENCES subscription_plans(id),
    stripe_price_id VARCHAR(255), -- Stripe price ID
    quantity INTEGER DEFAULT 1,
    
    -- Billing cycle
    billing_cycle_anchor TIMESTAMP WITH TIME ZONE,
    collection_method VARCHAR(20) DEFAULT 'charge_automatically', -- charge_automatically, send_invoice
    
    -- Amounts (in cents)
    amount_total INTEGER, -- Total amount including tax
    amount_subtotal INTEGER, -- Subtotal before tax
    tax_percent DECIMAL(5,2), -- Tax percentage applied
    
    -- Cancellation
    cancel_at_period_end BOOLEAN DEFAULT false,
    canceled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    
    -- Metadata and tracking
    metadata JSONB DEFAULT '{}',
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stripe invoices tracking
CREATE TABLE stripe_invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    stripe_invoice_id VARCHAR(255) NOT NULL UNIQUE, -- Stripe invoice ID
    stripe_customer_id VARCHAR(255) NOT NULL,
    stripe_subscription_id VARCHAR(255), -- May be null for one-time payments
    
    -- Invoice details
    number VARCHAR(100), -- Invoice number
    status VARCHAR(30) NOT NULL, -- draft, open, paid, void, uncollectible
    
    -- Amounts (in cents)
    amount_due INTEGER NOT NULL,
    amount_paid INTEGER DEFAULT 0,
    amount_remaining INTEGER DEFAULT 0,
    subtotal INTEGER NOT NULL,
    tax INTEGER DEFAULT 0,
    total INTEGER NOT NULL,
    
    -- Dates
    created TIMESTAMP WITH TIME ZONE NOT NULL,
    due_date TIMESTAMP WITH TIME ZONE,
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    
    -- Payment tracking
    paid BOOLEAN DEFAULT false,
    paid_at TIMESTAMP WITH TIME ZONE,
    payment_intent_id VARCHAR(255), -- Stripe PaymentIntent ID
    
    -- Invoice URLs
    hosted_invoice_url TEXT, -- Stripe-hosted invoice page
    invoice_pdf TEXT, -- PDF download URL
    
    -- Metadata
    description TEXT,
    footer TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Sync tracking
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stripe webhook events log
CREATE TABLE stripe_webhook_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stripe_event_id VARCHAR(255) NOT NULL UNIQUE, -- Stripe event ID
    
    -- Event details
    event_type VARCHAR(100) NOT NULL, -- customer.created, invoice.paid, etc.
    livemode BOOLEAN NOT NULL, -- true for production, false for test
    api_version VARCHAR(20), -- Stripe API version
    
    -- Event data
    data JSONB NOT NULL, -- Complete event data from Stripe
    previous_attributes JSONB, -- For *.updated events
    
    -- Processing status
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    processing_error TEXT, -- Error message if processing failed
    retry_count INTEGER DEFAULT 0,
    
    -- Webhook request details
    request_id VARCHAR(255), -- Stripe request ID
    webhook_endpoint_id VARCHAR(255), -- Webhook endpoint ID
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- STRIPE INTEGRATION FUNCTIONS
-- =============================================================================

-- Function to sync company subscription status from Stripe
CREATE OR REPLACE FUNCTION sync_company_subscription_status(
    company_id_param UUID,
    stripe_subscription_id_param VARCHAR(255),
    status_param VARCHAR(50)
) RETURNS VOID AS $$
BEGIN
    UPDATE companies 
    SET 
        subscription_status = CASE 
            WHEN status_param IN ('active', 'trialing') THEN 'active'
            WHEN status_param IN ('past_due', 'unpaid') THEN 'suspended'
            WHEN status_param IN ('canceled', 'incomplete_expired') THEN 'cancelled'
            ELSE subscription_status
        END,
        stripe_subscription_id = stripe_subscription_id_param,
        updated_at = NOW()
    WHERE id = company_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to update subscription limits based on plan
CREATE OR REPLACE FUNCTION update_subscription_limits(company_id_param UUID)
RETURNS VOID AS $$
DECLARE
    plan_limits RECORD;
BEGIN
    -- Get the current plan limits
    SELECT sp.max_users, sp.max_jobs, sp.max_candidates
    INTO plan_limits
    FROM companies c
    JOIN subscription_plans sp ON c.subscription_plan_id = sp.id
    WHERE c.id = company_id_param;
    
    IF FOUND THEN
        -- Update company limits (this would be used by application logic)
        UPDATE companies 
        SET settings = jsonb_set(
            jsonb_set(
                jsonb_set(
                    COALESCE(settings, '{}'),
                    '{max_users}', 
                    to_jsonb(plan_limits.max_users)
                ),
                '{max_jobs}', 
                to_jsonb(plan_limits.max_jobs)
            ),
            '{max_candidates}', 
            to_jsonb(plan_limits.max_candidates)
        )
        WHERE id = company_id_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to handle Stripe webhook events
CREATE OR REPLACE FUNCTION process_stripe_webhook_event(
    event_id_param VARCHAR(255),
    event_type_param VARCHAR(100),
    event_data_param JSONB
) RETURNS BOOLEAN AS $$
DECLARE
    subscription_data JSONB;
    customer_id VARCHAR(255);
    company_record RECORD;
BEGIN
    -- Mark event as being processed
    UPDATE stripe_webhook_events 
    SET processed = true, processed_at = NOW()
    WHERE stripe_event_id = event_id_param;
    
    -- Process different event types
    CASE event_type_param
        WHEN 'customer.subscription.updated', 'customer.subscription.created' THEN
            -- Extract subscription data
            subscription_data := event_data_param->'object';
            customer_id := subscription_data->>'customer';
            
            -- Find the company associated with this customer
            SELECT c.* INTO company_record
            FROM companies c
            JOIN stripe_customers sc ON c.id = sc.company_id
            WHERE sc.stripe_customer_id = customer_id;
            
            IF FOUND THEN
                -- Update subscription status
                PERFORM sync_company_subscription_status(
                    company_record.id,
                    subscription_data->>'id',
                    subscription_data->>'status'
                );
            END IF;
            
        WHEN 'invoice.paid' THEN
            -- Handle successful payment
            -- Additional logic can be added here
            RETURN TRUE;
            
        WHEN 'invoice.payment_failed' THEN
            -- Handle failed payment
            -- Additional logic can be added here
            RETURN TRUE;
            
        ELSE
            -- Unknown event type, log but don't fail
            RETURN TRUE;
    END CASE;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        UPDATE stripe_webhook_events 
        SET 
            processing_error = SQLERRM,
            retry_count = retry_count + 1,
            processed = false
        WHERE stripe_event_id = event_id_param;
        
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
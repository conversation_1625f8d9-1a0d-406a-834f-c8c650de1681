-- Migration 002: Candidates and Bench Management
-- Run this SECOND (after 001_core_tables.sql)

-- =============================================================================
-- CANDIDATE MANAGEMENT - UPDATED WITH CORRECT BENCH ID
-- =============================================================================

-- Sequence for Bench ID generation
CREATE SEQUENCE IF NOT EXISTS bench_id_collision_sequence START 1;

-- Candidates table - UPDATED with SSN field and corrected Bench ID
CREATE TABLE candidates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
    
    -- Unique bench ID: FFF+LLL+MMDD+SSSS+XX format
    bench_id VARCHAR(17) UNIQUE NOT NULL,
    
    -- Personal information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE, -- Required for MMDD in bench ID
    ssn_last_four VARCHAR(4), -- Last 4 digits of SSN (plain text, not encrypted)
    email VARCHAR(255),
    phone VARCHAR(20),
    alternate_email VARCHAR(255),
    alternate_phone VARCHAR(20),
    
    -- Location information
    current_location VARCHAR(255),
    preferred_locations TEXT[], -- Array of preferred work locations
    willing_to_relocate BOOLEAN DEFAULT false,
    
    -- Professional information
    current_title VARCHAR(255),
    current_company VARCHAR(255),
    total_experience_years DECIMAL(4,2),
    relevant_experience_years DECIMAL(4,2),
    
    -- Skills and expertise
    primary_skills TEXT[] NOT NULL, -- Core technical skills
    secondary_skills TEXT[], -- Additional skills
    certifications TEXT[], -- Professional certifications
    
    -- Availability and preferences
    availability_status VARCHAR(20) DEFAULT 'available', -- available, placed, interviewing, not_available
    availability_date DATE,
    notice_period_weeks INTEGER,
    
    -- Compensation expectations
    current_salary DECIMAL(12,2),
    expected_salary DECIMAL(12,2),
    salary_currency VARCHAR(3) DEFAULT 'USD',
    hourly_rate DECIMAL(8,2),
    
    -- Work preferences
    work_authorization VARCHAR(50), -- US Citizen, Green Card, H1B, etc.
    remote_preference VARCHAR(20) DEFAULT 'hybrid', -- remote, hybrid, onsite
    job_type_preference VARCHAR(20) DEFAULT 'permanent', -- permanent, contract, both
    
    -- Candidate source and tracking
    source VARCHAR(50), -- How candidate was acquired
    source_details TEXT,
    recruiter_id UUID REFERENCES users(id), -- Who recruited this candidate
    
    -- Search and matching (without pgvector for now)
    search_vector tsvector, -- Full-text search
    -- embedding vector(1536), -- Semantic search embeddings (requires pgvector)
    
    -- Resume fraud prevention
    document_hashes TEXT[], -- Array of document file hashes
    content_hashes TEXT[], -- Array of document content hashes
    duplicate_flags JSONB DEFAULT '{}', -- Fraud detection results
    
    -- Audit trail
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- BENCH ID GENERATION FUNCTIONS - CORRECTED
-- =============================================================================

-- Updated function for correct Bench ID generation: FFF+LLL+MMDD+SSSS+XX
CREATE OR REPLACE FUNCTION generate_bench_id(
    first_name_param VARCHAR(100),
    last_name_param VARCHAR(100),
    date_of_birth_param DATE,
    ssn_last_four_param VARCHAR(4)
) RETURNS VARCHAR(17) AS $$
DECLARE
    first_part VARCHAR(3);
    last_part VARCHAR(3);
    birth_part VARCHAR(4);
    ssn_part VARCHAR(4);
    collision_suffix VARCHAR(2) := '00';
    final_bench_id VARCHAR(17);
    base_bench_id VARCHAR(15);
    collision_counter INTEGER := 0;
BEGIN
    -- Get first 3 characters of first name (uppercase, letters only)
    first_part := UPPER(SUBSTRING(REGEXP_REPLACE(first_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    first_part := RPAD(first_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get first 3 characters of last name (uppercase, letters only)
    last_part := UPPER(SUBSTRING(REGEXP_REPLACE(last_name_param, '[^A-Za-z]', '', 'g') FROM 1 FOR 3));
    last_part := RPAD(last_part, 3, 'X'); -- Pad with X if less than 3 chars
    
    -- Get month and day from date of birth (MMDD format)
    birth_part := TO_CHAR(date_of_birth_param, 'MMDD');
    
    -- Use last 4 digits of SSN
    ssn_part := LPAD(COALESCE(ssn_last_four_param, '0000'), 4, '0');
    
    -- Create base Bench ID (15 characters)
    base_bench_id := first_part || last_part || birth_part || ssn_part;
    
    -- Handle collisions with 2-digit suffix (00-99)
    LOOP
        final_bench_id := base_bench_id || collision_suffix;
        
        -- Check if this ID already exists
        IF NOT EXISTS (SELECT 1 FROM candidates WHERE bench_id = final_bench_id) THEN
            EXIT; -- Found unique ID
        END IF;
        
        -- Increment collision counter
        collision_counter := collision_counter + 1;
        collision_suffix := LPAD(collision_counter::text, 2, '0');
        
        -- Safety check to prevent infinite loop
        IF collision_counter >= 99 THEN
            RAISE EXCEPTION 'Unable to generate unique Bench ID after 99 attempts for %', base_bench_id;
        END IF;
    END LOOP;
    
    RETURN final_bench_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate bench ID for candidates - UPDATED
CREATE OR REPLACE FUNCTION trigger_generate_bench_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.bench_id IS NULL OR NEW.bench_id = '' THEN
        -- Require date_of_birth and ssn_last_four for Bench ID generation
        IF NEW.date_of_birth IS NULL THEN
            RAISE EXCEPTION 'Date of birth is required for Bench ID generation';
        END IF;
        
        NEW.bench_id := generate_bench_id(
            NEW.first_name,
            NEW.last_name,
            NEW.date_of_birth,
            NEW.ssn_last_four
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER candidates_generate_bench_id
    BEFORE INSERT ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION trigger_generate_bench_id();

-- Function to update search vectors for candidates
CREATE OR REPLACE FUNCTION update_candidate_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.first_name, '') || ' ' ||
        COALESCE(NEW.last_name, '') || ' ' ||
        COALESCE(NEW.current_title, '') || ' ' ||
        COALESCE(NEW.current_company, '') || ' ' ||
        COALESCE(array_to_string(NEW.primary_skills, ' '), '') || ' ' ||
        COALESCE(array_to_string(NEW.secondary_skills, ' '), '') || ' ' ||
        COALESCE(NEW.current_location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER candidates_search_vector_update
    BEFORE INSERT OR UPDATE ON candidates
    FOR EACH ROW
    EXECUTE FUNCTION update_candidate_search_vector();

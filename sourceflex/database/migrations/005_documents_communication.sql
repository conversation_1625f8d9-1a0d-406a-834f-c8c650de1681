-- Migration 005: Documents and Communication Management
-- Run this FIFTH (after 004_stripe_integration.sql)

-- =============================================================================
-- DOCUMENT MANAGEMENT
-- =============================================================================

-- Document storage with fraud prevention
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
    
    -- Document metadata
    name VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- pdf, doc, docx, txt, etc.
    file_size BIGINT NOT NULL, -- Size in bytes
    mime_type VARCHAR(100),
    
    -- Storage information (nHost Storage)
    storage_path TEXT NOT NULL, -- Path in nHost Storage
    storage_url TEXT, -- Public URL if applicable
    
    -- Content analysis
    text_content TEXT, -- Extracted text for search
    search_vector tsvector, -- Full-text search
    
    -- Fraud prevention
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of file
    content_hash VARCHAR(64), -- Hash of extracted text content
    
    -- Duplicate detection results
    duplicate_of UUID REFERENCES documents(id), -- If this is a duplicate
    similarity_score DECIMAL(5,4), -- 0.0000 to 1.0000
    duplicate_flags JSONB DEFAULT '{}', -- Detailed fraud analysis
    
    -- Document categorization
    category VARCHAR(50) DEFAULT 'resume', -- resume, cover_letter, portfolio, certificate
    tags TEXT[], -- User-defined tags
    
    -- Version control
    version INTEGER DEFAULT 1,
    parent_document_id UUID REFERENCES documents(id), -- Previous version
    is_latest_version BOOLEAN DEFAULT true,
    
    -- Processing status
    processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    ocr_completed BOOLEAN DEFAULT false,
    text_extraction_completed BOOLEAN DEFAULT false,
    
    -- Audit trail
    uploaded_by UUID NOT NULL REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- COMMUNICATION TRACKING
-- =============================================================================

-- Message threads for organizing conversations
CREATE TABLE message_threads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Thread context
    subject VARCHAR(255) NOT NULL,
    thread_type VARCHAR(20) DEFAULT 'general', -- general, candidate, job, client, internal
    
    -- Related entities (optional references)
    candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
    
    -- Thread status
    status VARCHAR(20) DEFAULT 'active', -- active, archived, closed
    priority VARCHAR(10) DEFAULT 'normal', -- low, normal, high, urgent
    
    -- Participants
    participants UUID[] NOT NULL, -- Array of user IDs
    external_participants TEXT[], -- External email addresses
    
    -- Thread metadata
    last_message_at TIMESTAMP WITH TIME ZONE,
    message_count INTEGER DEFAULT 0,
    unread_count INTEGER DEFAULT 0,
    
    -- Audit
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual messages within threads
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    thread_id UUID NOT NULL REFERENCES message_threads(id) ON DELETE CASCADE,
    
    -- Message content
    subject VARCHAR(255), -- Optional for thread messages
    body TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'email', -- email, call, note, sms
    
    -- Direction and participants
    direction VARCHAR(10) DEFAULT 'outbound', -- inbound, outbound, internal
    from_user_id UUID REFERENCES user_profiles(id), -- Internal sender
    from_email VARCHAR(255), -- External sender email
    from_name VARCHAR(255), -- External sender name
    
    -- Recipients
    to_emails TEXT[] DEFAULT '{}', -- External recipient emails
    cc_emails TEXT[] DEFAULT '{}', -- CC recipients
    bcc_emails TEXT[] DEFAULT '{}', -- BCC recipients
    internal_recipients UUID[] DEFAULT '{}', -- Internal user IDs
    
    -- Message metadata
    external_message_id VARCHAR(255), -- Original email message ID
    in_reply_to VARCHAR(255), -- Original message this replies to
    references TEXT[], -- Message reference chain
    
    -- Attachments
    has_attachments BOOLEAN DEFAULT false,
    attachment_count INTEGER DEFAULT 0,
    attachment_documents UUID[], -- References to documents table
    
    -- Status tracking
    is_read BOOLEAN DEFAULT false,
    read_by UUID[], -- Array of user IDs who have read this
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Email specific
    bounce_status VARCHAR(20), -- delivered, bounced, spam, etc.
    open_tracking BOOLEAN DEFAULT false,
    opened_at TIMESTAMP WITH TIME ZONE,
    click_tracking BOOLEAN DEFAULT false,
    
    -- Search
    search_vector tsvector,
    
    -- Audit
    created_by UUID REFERENCES user_profiles(id), -- May be null for inbound
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- DOCUMENT PROCESSING FUNCTIONS
-- =============================================================================

-- Function to update document search vector
CREATE OR REPLACE FUNCTION update_document_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.name, '') || ' ' ||
        COALESCE(NEW.original_filename, '') || ' ' ||
        COALESCE(NEW.text_content, '') || ' ' ||
        COALESCE(array_to_string(NEW.tags, ' '), '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER documents_search_vector_update
    BEFORE INSERT OR UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_document_search_vector();

-- Function to detect document duplicates
CREATE OR REPLACE FUNCTION detect_document_duplicates()
RETURNS TRIGGER AS $$
DECLARE
    existing_doc RECORD;
BEGIN
    -- Check for exact file hash match
    SELECT d.*, 1.0000 as sim INTO existing_doc
    FROM documents d
    WHERE d.file_hash = NEW.file_hash 
    AND d.id != COALESCE(NEW.id, uuid_generate_v4())
    AND d.company_id = NEW.company_id
    LIMIT 1;
    
    IF FOUND THEN
        NEW.duplicate_of := existing_doc.id;
        NEW.similarity_score := existing_doc.sim;
        NEW.duplicate_flags := jsonb_build_object(
            'type', 'exact_file_match',
            'original_document_id', existing_doc.id,
            'detection_method', 'file_hash'
        );
        RETURN NEW;
    END IF;
    
    -- Check for content hash match (if available)
    IF NEW.content_hash IS NOT NULL THEN
        SELECT d.*, 0.9500 as sim INTO existing_doc
        FROM documents d
        WHERE d.content_hash = NEW.content_hash 
        AND d.id != COALESCE(NEW.id, uuid_generate_v4())
        AND d.company_id = NEW.company_id
        LIMIT 1;
        
        IF FOUND THEN
            NEW.duplicate_of := existing_doc.id;
            NEW.similarity_score := existing_doc.sim;
            NEW.duplicate_flags := jsonb_build_object(
                'type', 'content_match',
                'original_document_id', existing_doc.id,
                'detection_method', 'content_hash'
            );
            RETURN NEW;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER documents_duplicate_detection
    BEFORE INSERT OR UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION detect_document_duplicates();

-- Function to update message search vector
CREATE OR REPLACE FUNCTION update_message_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.subject, '') || ' ' ||
        COALESCE(NEW.body, '') || ' ' ||
        COALESCE(NEW.from_name, '') || ' ' ||
        COALESCE(NEW.from_email, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER messages_search_vector_update
    BEFORE INSERT OR UPDATE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_message_search_vector();

-- Function to update thread message counts
CREATE OR REPLACE FUNCTION update_thread_message_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE message_threads 
        SET 
            message_count = message_count + 1,
            last_message_at = NEW.created_at,
            updated_at = NOW()
        WHERE id = NEW.thread_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE message_threads 
        SET 
            message_count = GREATEST(message_count - 1, 0),
            updated_at = NOW()
        WHERE id = OLD.thread_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER messages_update_thread_counts
    AFTER INSERT OR DELETE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_thread_message_counts();

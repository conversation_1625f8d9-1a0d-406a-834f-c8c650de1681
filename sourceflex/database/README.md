# SourceFlex Database Schema

This directory contains the complete database schema and documentation for SourceFlex, a multi-tenant recruitment and bench sales platform.

## Overview

SourceFlex uses PostgreSQL with the following extensions:
- **pgvector**: For semantic search capabilities using embeddings
- **uuid-ossp**: For UUID generation
- **pgcrypto**: For cryptographic functions

## Schema Architecture

### Multi-Tenancy
- **Company-level isolation**: All data is scoped to companies
- **Desk-based separation**: Within companies, data is separated by desk type (Recruitment vs Bench Sales)
- **Row Level Security (RLS)**: Enforced at the database level

### Core Entities

#### 1. Tenant Management
- `companies` - Multi-tenant organization management
- `subscription_plans` - Feature gating and pricing tiers
- `desks` - Department separation (Recruitment/BenchSales)

#### 2. User Management
- `users` - User profiles linked to nHost authentication
- `roles` - Permission-based access control

#### 3. Business Entities
- `clients` - Customer/client companies
- `jobs` - Job postings with semantic search
- `candidates` - Candidate profiles with unique Bench IDs
- `documents` - Document management with fraud prevention

#### 4. Workflow Management
- `submissions` - Candidate-to-job submissions
- `interviews` - Interview scheduling and feedback
- `placements` - Successful hires with guarantee tracking
- `communications` - Email/call/note tracking

#### 5. System Features
- `notifications` - Real-time alerts and reminders
- `activity_logs` - Comprehensive audit trail
- `saved_searches` - User-defined search queries
- `search_analytics` - Search behavior tracking

## Unique Features

### 1. Bench ID System (FFF+LLL+MMDD+SSSS)
```sql
-- Example: JOHSMI121501234
-- JOH = First 3 letters of first name
-- SMI = First 3 letters of last name  
-- 1215 = Month/Day (December 15th)
-- 0001 = Sequence number
```

### 2. Resume Fraud Prevention
- File hash comparison for duplicate detection
- Content hash for text-based duplicate detection  
- Version tracking with change detection
- OCR text extraction for searchability

### 3. Semantic Search
- Vector embeddings for jobs, candidates, and documents
- Full-text search with weighted relevance
- Combined traditional + AI-powered search

### 4. Subscription-based Feature Gating
- Tiered access to advanced features
- Usage limits enforced at database level
- Automatic feature enablement/disabling

## Database Functions

### Auto-generated Bench IDs
```sql
SELECT generate_bench_id('John', 'Smith');
-- Returns: JOHSMI121501234
```

### Search Vector Updates
- Automatic full-text search vector generation
- Triggered on INSERT/UPDATE operations
- Weighted content relevance

### Activity Logging
- Automatic audit trail for all major operations
- Change tracking with old/new value comparison
- User attribution and timestamp tracking

## Performance Optimizations

### Indexes
- Composite indexes for common query patterns
- GIN indexes for full-text search
- IVFFlat indexes for vector similarity search
- Partial indexes for filtered queries

### Views
- `active_candidates` - Candidates with computed metrics
- `active_jobs` - Jobs with submission counts
- `dashboard_metrics` - Real-time dashboard data

## Security

### Row Level Security (RLS)
- Company-level data isolation
- User role-based access control
- Helper functions for permission checking

### Data Protection
- Encrypted sensitive fields
- Audit trail for compliance
- Automatic data anonymization options

## Files

- `schema.sql` - Complete database schema
- `migrations/` - Database migration files (future)
- `seeds/` - Initial data and test fixtures (future)
- `types.ts` - TypeScript type definitions (generated)

## Usage with nHost

This schema is designed to work with nHost's managed PostgreSQL service:

1. **Authentication**: Integrates with nHost Auth via `auth.uid()`
2. **Storage**: References nHost Storage for file management
3. **Real-time**: Compatible with nHost Real-time subscriptions
4. **GraphQL**: Optimized for Hasura auto-generated GraphQL API

## Development

### Setting Up
1. Deploy schema to nHost PostgreSQL instance
2. Run GraphQL Codegen to generate types
3. Set up RLS policies for your environment
4. Configure Hasura permissions

### Making Changes
1. Always create migration files
2. Update TypeScript types
3. Test RLS policies thoroughly
4. Update documentation

## Monitoring

Key metrics to monitor:
- Query performance on large tables
- Index usage statistics  
- RLS policy evaluation time
- Vector search performance
- Storage usage for documents

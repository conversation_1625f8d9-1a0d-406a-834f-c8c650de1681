# Database Conflict Resolution Guide

## Problem Summary
The Hasura "Adding existing table/view failed" error occurs because:
- Migration 001 created a `users` table in `public` schema
- Nhost automatically provides `auth.users` table in `auth` schema  
- Both tables generate conflicting GraphQL operations (`update_users_many`)

## Solution Applied
Created migration 008 to resolve conflicts by:
1. Renaming `users` → `user_profiles`
2. Removing duplicate fields (email, names already in `auth.users`)
3. Updating all foreign key references
4. Updating RLS policies and indexes
5. Fixing views and functions

## Steps to Apply Fix

### 1. Run the Patch Migration
```sql
-- In Hasura Console SQL tab or psql:
\i /path/to/008_fix_users_table_conflict.sql
```

### 2. Verify Tables After Migration
Check these tables exist and have correct structure:
- ✅ `user_profiles` (renamed from users)
- ✅ `auth.users` (Nhost authentication)
- ✅ All foreign keys point to `user_profiles`

### 3. Track Tables in Hasura
After running migration 008:
1. Go to Hasura Console → Data tab
2. Click "Track All" button
3. All tables should track without conflicts

### 4. Expected Results
- ✅ No more GraphQL field conflicts
- ✅ `auth.users` handles authentication
- ✅ `user_profiles` handles business logic
- ✅ Clear separation of concerns

## Database Structure After Fix

### Authentication Flow
```
auth.users (Nhost)     user_profiles (Business)
├── id (UUID)    ←────→ id (references auth.users.id)
├── email              ├── company_id
├── first_name         ├── desk_id  
├── last_name          ├── role_id
└── auth_data          └── business_data
```

### Foreign Key References
All business tables now reference `user_profiles`:
- `candidates.recruiter_id` → `user_profiles.id`
- `documents.uploaded_by` → `user_profiles.id`
- `messages.created_by` → `user_profiles.id`
- etc.

## Verification Commands

### Check Table Exists
```sql
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_name IN ('users', 'user_profiles') 
AND table_schema = 'public';
```

### Check Foreign Keys
```sql
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND ccu.table_name = 'user_profiles';
```

### Test Hasura Tracking
After migration, try tracking tables in Hasura console. Should succeed without conflicts.

## Rollback (If Needed)
If you need to rollback this migration:
```sql
-- Emergency rollback (not recommended after data changes)
ALTER TABLE user_profiles RENAME TO users;
-- Then manually restore dropped columns and constraints
```

## Next Steps
1. ✅ Apply migration 008
2. ✅ Track all tables in Hasura  
3. ✅ Set up relationships between tables
4. ✅ Configure permissions for each table
5. ✅ Test GraphQL operations

The migration preserves all your data while resolving the authentication table conflicts.

-- Run this in Hasura Console SQL tab AFTER migration 008
-- Migration 005: Documents and Communication (Simplified)

CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    original_filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    storage_path TEXT NOT NULL,
    storage_url TEXT,
    text_content TEXT,
    search_vector tsvector,
    file_hash VARCHAR(64) NOT NULL,
    content_hash VARCHAR(64),
    duplicate_of UUID REFERENCES documents(id),
    similarity_score DECIMAL(5,4),
    duplicate_flags JSONB DEFAULT '{}',
    category VARCHAR(50) DEFAULT 'resume',
    tags TEXT[],
    version INTEGER DEFAULT 1,
    parent_document_id UUID REFERENCES documents(id),
    is_latest_version B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    processing_status VARCHAR(20) DEFAULT 'pending',
    ocr_completed BOOLEAN DEFAULT false,
    text_extraction_completed BOOLEAN DEFAULT false,
    uploaded_by UUID NOT NULL REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE message_threads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    subject VARCHAR(255) NOT NULL,
    thread_type VARCHAR(20) DEFAULT 'general',
    candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'active',
    priority VARCHAR(10) DEFAULT 'normal',
    participants UUID[] NOT NULL,
    external_participants TEXT[],
    last_message_at TIMESTAMP WITH TIME ZONE,
    message_count INTEGER DEFAULT 0,
    unread_count INTEGER DEFAULT 0,
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    thread_id UUID NOT NULL REFERENCES message_threads(id) ON DELETE CASCADE,
    subject VARCHAR(255),
    body TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'email',
    direction VARCHAR(10) DEFAULT 'outbound',
    from_user_id UUID REFERENCES user_profiles(id),
    from_email VARCHAR(255),
    from_name VARCHAR(255),
    to_emails TEXT[] DEFAULT '{}',
    cc_emails TEXT[] DEFAULT '{}',
    bcc_emails TEXT[] DEFAULT '{}',
    internal_recipients UUID[] DEFAULT '{}',
    external_message_id VARCHAR(255),
    in_reply_to VARCHAR(255),
    references TEXT[],
    has_attachments BOOLEAN DEFAULT false,
    attachment_count INTEGER DEFAULT 0,
    attachment_documents UUID[],
    is_read BOOLEAN DEFAULT false,
    read_by UUID[],
    read_at TIMESTAMP WITH TIME ZONE,
    bounce_status VARCHAR(20),
    open_tracking BOOLEAN DEFAULT false,
    opened_at TIMESTAMP WITH TIME ZONE,
    click_tracking BOOLEAN DEFAULT false,
    search_vector tsvector,
    created_by UUID REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

SELECT 'Migration 005 completed successfully!' as result;
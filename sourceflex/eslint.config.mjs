import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    rules: {
      // React-specific rules
      "react/jsx-key": "error",
      "react/jsx-no-bind": ["error", { allowArrowFunctions: true }],
      "react/jsx-no-constructed-context-values": "error",
      "react/jsx-no-leaked-render": "error",
      "react/jsx-no-useless-fragment": "error",
      "react/no-array-index-key": "warn",
      "react/no-unstable-nested-components": "error",

      // Basic TypeScript rules (no type checking required)
      "@typescript-eslint/no-unused-vars": ["error", { 
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        ignoreRestSiblings: true 
      }],
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/consistent-type-imports": [
        "error",
        { prefer: "type-imports", fixStyle: "inline-type-imports" }
      ],

      // Import organization rules
      "sort-imports": ["error", { 
        ignoreCase: false,
        ignoreDeclarationSort: true,
        ignoreMemberSort: false,
        memberSyntaxSortOrder: ["none", "all", "multiple", "single"],
        allowSeparatedGroups: true
      }],

      // General code quality rules
      "no-console": "warn",
      "no-debugger": "error",
      "no-alert": "error",
      "no-var": "error",
      "prefer-const": "error",
      "prefer-template": "error",
      "object-shorthand": "error",
      "no-duplicate-imports": "error",
      "no-useless-return": "error",
      "no-unreachable": "error",

      // Code complexity rules
      "complexity": ["warn", 10],
      "max-depth": ["warn", 4],
      "max-lines": ["warn", 300],
      "max-lines-per-function": ["warn", 50],
      "max-params": ["warn", 4],

      // Naming conventions
      "camelcase": ["error", { properties: "never", ignoreDestructuring: true }],

      // Next.js specific rules (enhanced)
      "@next/next/no-img-element": "error",
      "@next/next/no-page-custom-font": "error",
      "@next/next/no-head-element": "error",
      "@next/next/no-script-component-in-head": "error",
    }
  },
  {
    // More relaxed rules for configuration files
    files: ["*.config.{js,ts,mjs}", "tailwind.config.{js,ts}", "next.config.{js,ts}"],
    rules: {
      "no-console": "off",
      "@typescript-eslint/no-var-requires": "off",
    }
  },
  {
    // Ignore patterns
    ignores: [
      ".next/**",
      "node_modules/**",
      "public/**",
      "**/*.d.ts",
      "src/types/generated/**", // GraphQL generated types
    ]
  }
];

export default eslintConfig;
